// Professional template library for DocMagic
// Real, industry-standard templates for professional document creation
export const professionalTemplates = [
  // RESUME TEMPLATES
  {
    id: '1',
    user_id: 'system',
    title: 'Software Engineer Resume',
    description: 'Professional resume template optimized for software engineering positions with emphasis on technical skills and project experience',
    type: 'resume',
    content: {
      personalInfo: {
        name: '[Your Full Name]',
        email: '[<EMAIL>]',
        phone: '[Your Phone Number]',
        location: '[City, State]',
        website: '[portfolio-website.com]',
        linkedin: '[linkedin.com/in/yourprofile]',
        github: '[github.com/yourusername]',
        summary: 'Results-driven Software Engineer with [X] years of experience developing scalable web applications and systems. Proficient in modern programming languages and frameworks with a strong foundation in software engineering principles, data structures, and algorithms. Proven track record of delivering high-quality code and collaborating effectively in agile development environments.'
      },
      sections: [
        {
          id: 'technical-skills',
          title: 'Technical Skills',
          items: [
            {
              category: 'Programming Languages',
              skills: 'JavaScript (ES6+), TypeScript, Python, Java, C++, Go, SQL',
              proficiency: 'Expert in JavaScript/TypeScript, Proficient in Python/Java'
            },
            {
              category: 'Frontend Technologies',
              skills: 'React.js, Next.js, Vue.js, Angular, HTML5, CSS3, Sass, Tailwind CSS, Material-UI',
              experience: '5+ years with React ecosystem, 3+ years with Vue.js'
            },
            {
              category: 'Backend Technologies',
              skills: 'Node.js, Express.js, Django, Flask, Spring Boot, RESTful APIs, GraphQL, Microservices',
              experience: 'Built scalable APIs serving 100K+ requests/day'
            },
            {
              category: 'Databases & Storage',
              skills: 'PostgreSQL, MySQL, MongoDB, Redis, Elasticsearch, AWS S3, Firebase',
              experience: 'Optimized queries for databases with 10M+ records'
            },
            {
              category: 'Cloud & DevOps',
              skills: 'AWS (EC2, Lambda, RDS, S3), Docker, Kubernetes, CI/CD, Jenkins, GitHub Actions, Terraform',
              certifications: 'AWS Solutions Architect Associate'
            },
            {
              category: 'Development Tools',
              skills: 'Git, VS Code, IntelliJ IDEA, Postman, Figma, Jira, Confluence',
              experience: 'Daily use in collaborative development environments'
            },
            {
              category: 'Methodologies',
              skills: 'Agile/Scrum, Test-Driven Development, Code Review, Pair Programming, DevOps',
              experience: 'Led Scrum teams of 5-8 developers'
            }
          ]
        },
        {
          id: 'experience',
          title: 'Professional Experience',
          items: [
            {
              position: 'Senior Software Engineer',
              company: 'TechCorp Solutions',
              location: 'San Francisco, CA',
              duration: 'January 2022 - Present',
              achievements: [
                'Led development of microservices architecture serving 500K+ daily active users, improving system reliability by 99.9%',
                'Implemented real-time data processing pipeline using Apache Kafka and Redis, reducing data latency by 75%',
                'Collaborated with product and design teams to deliver 15+ features on schedule, increasing user engagement by 40%',
                'Optimized database queries and implemented caching strategies, reducing API response times from 800ms to 120ms',
                'Mentored 4 junior developers and established code review processes that reduced production bugs by 60%',
                'Built automated testing suite with 90% code coverage, reducing QA cycle time by 50%'
              ],
              technologies: 'React, Node.js, PostgreSQL, AWS, Docker, Kubernetes'
            },
            {
              position: 'Software Engineer',
              company: 'StartupXYZ',
              location: 'Austin, TX',
              duration: 'June 2020 - December 2021',
              achievements: [
                'Developed full-stack web application from scratch using React and Django, supporting 50K+ users',
                'Integrated third-party APIs (Stripe, SendGrid, Twilio) to enable payment processing and communications',
                'Implemented responsive design principles, improving mobile user experience and increasing mobile traffic by 35%',
                'Built CI/CD pipeline using GitHub Actions, reducing deployment time from 2 hours to 15 minutes',
                'Collaborated in agile environment with daily standups, sprint planning, and retrospectives'
              ],
              technologies: 'React, Django, PostgreSQL, AWS EC2, GitHub Actions'
            },
            {
              position: 'Junior Software Developer',
              company: 'Digital Agency Pro',
              location: 'Remote',
              duration: 'August 2019 - May 2020',
              achievements: [
                'Developed and maintained 10+ client websites using modern web technologies',
                'Implemented SEO best practices, improving client search rankings by average of 40%',
                'Created reusable component library, reducing development time for new projects by 30%',
                'Participated in client meetings and provided technical consultation on project requirements'
              ],
              technologies: 'JavaScript, HTML5, CSS3, WordPress, PHP, MySQL'
            }
          ]
        },
        {
          id: 'projects',
          title: 'Key Projects',
          items: [
            {
              name: 'E-Commerce Platform Redesign',
              technologies: 'React, Node.js, PostgreSQL, Stripe API, AWS',
              description: 'Led complete redesign and development of e-commerce platform for mid-size retailer',
              achievements: [
                'Increased conversion rate by 45% through improved UX and performance optimization',
                'Reduced page load times from 3.2s to 0.8s through code splitting and caching strategies',
                'Implemented secure payment processing handling $2M+ in annual transactions'
              ],
              link: 'github.com/username/ecommerce-platform',
              duration: '6 months',
              team_size: '4 developers'
            },
            {
              name: 'Real-Time Analytics Dashboard',
              technologies: 'Vue.js, D3.js, WebSocket, Redis, Docker',
              description: 'Built real-time analytics dashboard for monitoring application performance and user behavior',
              achievements: [
                'Processed and visualized 100K+ events per minute with sub-second latency',
                'Reduced incident response time by 60% through proactive monitoring alerts',
                'Saved company $50K annually by identifying and fixing performance bottlenecks'
              ],
              link: 'github.com/username/analytics-dashboard',
              duration: '4 months',
              team_size: '2 developers'
            },
            {
              name: 'Mobile-First Progressive Web App',
              technologies: 'React Native, TypeScript, Firebase, Push Notifications',
              description: 'Developed cross-platform mobile application for task management and team collaboration',
              achievements: [
                'Achieved 4.8/5 star rating on app stores with 10K+ downloads in first month',
                'Implemented offline-first architecture supporting 99.9% uptime',
                'Reduced development time by 40% using shared codebase for iOS and Android'
              ],
              link: 'github.com/username/task-manager-pwa',
              duration: '8 months',
              team_size: 'Solo project'
            }
          ]
        },
        {
          id: 'education',
          title: 'Education',
          items: [
            {
              degree: 'Bachelor of Science in Computer Science',
              institution: 'University of California, Berkeley',
              location: 'Berkeley, CA',
              graduation: 'May 2019',
              gpa: '3.7/4.0',
              honors: 'Magna Cum Laude, Dean\'s List (6 semesters)',
              relevant_coursework: 'Data Structures & Algorithms, Software Engineering, Database Systems, Computer Networks, Machine Learning, Operating Systems, Web Development',
              senior_project: 'Distributed Task Scheduling System using Kubernetes and Go',
              activities: 'Computer Science Student Association (Vice President), ACM Programming Contest Team'
            },
            {
              degree: 'Associate of Science in Mathematics',
              institution: 'Community College of San Francisco',
              location: 'San Francisco, CA',
              graduation: 'May 2017',
              gpa: '3.9/4.0',
              honors: 'Phi Theta Kappa Honor Society',
              transfer_credits: 'Successfully transferred 60 credits to UC Berkeley'
            }
          ]
        },
        {
          id: 'certifications',
          title: 'Certifications & Awards',
          items: [
            {
              name: 'AWS Certified Solutions Architect - Associate',
              issuer: 'Amazon Web Services',
              date: 'March 2023',
              credential_id: 'AWS-ASA-12345',
              expiration: 'March 2026',
              verification_url: 'aws.amazon.com/verification'
            },
            {
              name: 'Certified Kubernetes Administrator (CKA)',
              issuer: 'Cloud Native Computing Foundation',
              date: 'January 2023',
              credential_id: 'CKA-2023-001234',
              expiration: 'January 2026'
            },
            {
              name: 'Google Cloud Professional Developer',
              issuer: 'Google Cloud',
              date: 'September 2022',
              credential_id: 'GCP-PD-567890',
              expiration: 'September 2024'
            },
            {
              name: 'Outstanding Graduate Award',
              issuer: 'UC Berkeley Computer Science Department',
              date: 'May 2019',
              description: 'Awarded to top 5% of graduating class for academic excellence and leadership'
            },
            {
              name: 'Hackathon Winner - Best Technical Innovation',
              issuer: 'TechCrunch Disrupt SF',
              date: 'October 2022',
              description: 'Led team to victory with AI-powered code review tool, competing against 200+ teams'
            }
          ]
        }
      ]
    },
    is_public: true,
    is_default: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    tags: ['software-engineer', 'technical', 'programming', 'development'],
    difficulty_level: 'intermediate',
    usage_count: 2847,
    rating: 4.9,
    preview_image: '/api/templates/1/preview',
    color_scheme: 'blue',
    industry: 'technology'
  },
  {
    id: '2',
    user_id: 'system',
    title: 'Marketing Manager Resume',
    description: 'Strategic marketing resume template highlighting campaign management, analytics, and growth achievements',
    type: 'resume',
    content: {
      personalInfo: {
        name: '[Your Full Name]',
        email: '[<EMAIL>]',
        phone: '[Your Phone Number]',
        location: '[City, State]',
        website: '[your-portfolio.com]',
        linkedin: '[linkedin.com/in/yourprofile]',
        summary: 'Strategic Marketing Manager with [X] years of experience driving brand growth and customer acquisition through data-driven campaigns. Expertise in digital marketing, content strategy, and cross-functional team leadership. Proven track record of increasing revenue by [X]% and improving customer engagement across multiple channels.'
      },
      sections: [
        {
          id: 'core-competencies',
          title: 'Core Competencies',
          items: [
            {
              category: 'Digital Marketing',
              skills: 'SEO/SEM, Social Media Marketing, Email Marketing, Content Marketing, PPC Advertising'
            },
            {
              category: 'Analytics & Tools',
              skills: 'Google Analytics, HubSpot, Salesforce, Adobe Creative Suite, Hootsuite, Mailchimp'
            },
            {
              category: 'Strategy & Planning',
              skills: 'Market Research, Campaign Development, Brand Management, Budget Planning, ROI Analysis'
            },
            {
              category: 'Leadership & Communication',
              skills: 'Team Management, Stakeholder Relations, Presentation Skills, Cross-functional Collaboration'
            }
          ]
        },
        {
          id: 'experience',
          title: 'Professional Experience',
          items: [
            {
              position: '[Marketing Manager/Senior Marketing Specialist]',
              company: '[Company Name]',
              location: '[City, State]',
              duration: '[Start Date] - [End Date/Present]',
              achievements: [
                'Led integrated marketing campaigns that generated $[amount] in revenue and increased brand awareness by [percentage]',
                'Managed marketing budget of $[amount] across [number] channels, achieving [percentage] ROI improvement',
                'Developed and executed content strategy that increased website traffic by [percentage] and lead generation by [percentage]',
                'Collaborated with sales team to create lead nurturing campaigns, resulting in [percentage] increase in conversion rates',
                'Analyzed campaign performance using Google Analytics and marketing automation tools to optimize future strategies'
              ]
            }
          ]
        },
        {
          id: 'achievements',
          title: 'Key Achievements',
          items: [
            {
              achievement: 'Campaign Excellence Award for [specific campaign] that exceeded targets by [percentage]'
            },
            {
              achievement: 'Increased social media engagement by [percentage] through strategic content planning and community management'
            },
            {
              achievement: 'Successfully launched [number] product campaigns, generating $[amount] in first-year revenue'
            }
          ]
        },
        {
          id: 'education',
          title: 'Education',
          items: [
            {
              degree: '[Degree] in Marketing/Business/Communications',
              institution: '[University Name]',
              location: '[City, State]',
              graduation: '[Graduation Date]',
              relevant_coursework: 'Digital Marketing, Consumer Behavior, Market Research, Brand Management, Statistics'
            }
          ]
        },
        {
          id: 'certifications',
          title: 'Professional Certifications',
          items: [
            {
              name: 'Google Analytics Certified',
              issuer: 'Google',
              date: '[Date]'
            },
            {
              name: 'HubSpot Content Marketing Certification',
              issuer: 'HubSpot Academy',
              date: '[Date]'
            },
            {
              name: 'Facebook Blueprint Certification',
              issuer: 'Meta',
              date: '[Date]'
            }
          ]
        }
      ]
    },
    is_public: true,
    is_default: true,
    created_at: '2024-01-02T00:00:00Z',
    updated_at: '2024-01-02T00:00:00Z',
    tags: ['marketing', 'digital-marketing', 'campaign-management', 'analytics'],
    difficulty_level: 'intermediate',
    usage_count: 1892,
    rating: 4.7,
    preview_image: '/api/templates/2/preview',
    color_scheme: 'green',
    industry: 'marketing'
  },
  {
    id: '3',
    user_id: 'system',
    title: 'Healthcare Professional Resume',
    description: 'Comprehensive resume template for nurses, medical technicians, and healthcare professionals',
    type: 'resume',
    content: {
      personalInfo: {
        name: '[Your Full Name]',
        email: '[<EMAIL>]',
        phone: '[Your Phone Number]',
        location: '[City, State]',
        license: '[License Number and State]',
        summary: 'Compassionate and dedicated Healthcare Professional with [X] years of experience providing exceptional patient care in [specialty/setting]. Committed to evidence-based practice and continuous professional development. Proven ability to work effectively in fast-paced environments while maintaining the highest standards of patient safety and care quality.'
      },
      sections: [
        {
          id: 'licenses-certifications',
          title: 'Licenses & Certifications',
          items: [
            {
              credential: '[Professional License] - [State]',
              number: '[License Number]',
              expiration: '[Expiration Date]'
            },
            {
              credential: 'BLS (Basic Life Support)',
              issuer: 'American Heart Association',
              expiration: '[Date]'
            },
            {
              credential: 'ACLS (Advanced Cardiovascular Life Support)',
              issuer: 'American Heart Association',
              expiration: '[Date]'
            }
          ]
        },
        {
          id: 'clinical-experience',
          title: 'Clinical Experience',
          items: [
            {
              position: '[Job Title]',
              facility: '[Healthcare Facility Name]',
              location: '[City, State]',
              duration: '[Start Date] - [End Date/Present]',
              unit_specialty: '[Unit/Department/Specialty]',
              responsibilities: [
                'Provided direct patient care for [number] patients per shift in [specialty] setting',
                'Administered medications and treatments according to physician orders and facility protocols',
                'Collaborated with interdisciplinary healthcare team to develop and implement patient care plans',
                'Documented patient assessments, interventions, and outcomes in electronic health records',
                'Educated patients and families on treatment plans, medications, and discharge instructions'
              ]
            }
          ]
        },
        {
          id: 'clinical-skills',
          title: 'Clinical Skills & Competencies',
          items: [
            {
              category: 'Patient Care',
              skills: 'Patient Assessment, Medication Administration, Wound Care, IV Therapy, Patient Education'
            },
            {
              category: 'Technical Skills',
              skills: 'Electronic Health Records (Epic, Cerner), Medical Equipment Operation, Vital Signs Monitoring'
            },
            {
              category: 'Specialized Procedures',
              skills: '[List relevant procedures based on specialty]'
            }
          ]
        },
        {
          id: 'education',
          title: 'Education',
          items: [
            {
              degree: '[Degree] in [Field]',
              institution: '[School/University Name]',
              location: '[City, State]',
              graduation: '[Graduation Date]',
              gpa: '[GPA if 3.5 or higher]',
              honors: '[Dean\'s List, Magna Cum Laude, etc.]'
            }
          ]
        },
        {
          id: 'professional-development',
          title: 'Professional Development',
          items: [
            {
              activity: '[Conference/Training Name]',
              organization: '[Organizing Body]',
              date: '[Date]',
              description: 'Brief description of learning outcomes'
            }
          ]
        }
      ]
    },
    is_public: true,
    is_default: true,
    created_at: '2024-01-03T00:00:00Z',
    updated_at: '2024-01-03T00:00:00Z',
    tags: ['healthcare', 'nursing', 'medical', 'patient-care'],
    difficulty_level: 'intermediate',
    usage_count: 1456,
    rating: 4.8,
    preview_image: '/api/templates/3/preview',
    color_scheme: 'teal',
    industry: 'healthcare'
  },
  // PRESENTATION TEMPLATES
  {
    id: '4',
    user_id: 'system',
    title: 'Startup Pitch Deck',
    description: 'Comprehensive pitch deck template for startups seeking investment, covering all essential elements investors expect',
    type: 'presentation',
    content: {
      title: 'Startup Pitch Deck',
      theme: 'modern-business',
      slides: [
        {
          id: '1',
          type: 'title',
          content: {
            title: 'TaskFlow Pro',
            subtitle: 'Streamlined project management for growing teams',
            presenter: 'Sarah Johnson, CEO & Co-Founder',
            date: 'January 2024',
            company_logo: '/logo-placeholder.png',
            contact: '<EMAIL>'
          }
        },
        {
          id: '2',
          type: 'content',
          content: {
            title: 'The Problem',
            subtitle: 'Small businesses struggle with inefficient project management',
            bullets: [
              '73% of small businesses use outdated tools like email and spreadsheets for project management',
              'Average project overruns budget by 27% and timeline by 35% due to poor coordination',
              'Existing solutions are too complex and expensive for teams under 20 people',
              'Remote work has amplified coordination challenges, with 68% reporting decreased productivity'
            ],
            notes: 'Based on survey of 1,200 small business owners conducted in Q3 2023',
            supporting_data: {
              market_research: 'Small Business Project Management Survey 2023',
              sample_size: '1,200 businesses with 5-50 employees',
              key_insight: 'Average cost of project delays: $15,000 per project'
            }
          }
        },
        {
          id: '3',
          type: 'content',
          content: {
            title: 'The Solution',
            subtitle: 'How you solve this problem uniquely',
            bullets: [
              '[Your solution in simple terms]',
              '[Key features that address the problem]',
              '[What makes your approach different/better]',
              '[Demo or visual representation of your product]'
            ],
            notes: 'Focus on benefits, not features. Show, don\'t just tell.'
          }
        },
        {
          id: '4',
          type: 'content',
          content: {
            title: 'Market Opportunity',
            subtitle: 'Size and growth potential',
            bullets: [
              'Total Addressable Market (TAM): $[X] billion',
              'Serviceable Addressable Market (SAM): $[X] billion',
              'Serviceable Obtainable Market (SOM): $[X] million',
              'Market growth rate: [X]% annually',
              'Key market trends supporting growth'
            ],
            notes: 'Use credible sources. Be realistic about your obtainable market.'
          }
        },
        {
          id: '5',
          type: 'content',
          content: {
            title: 'Business Model',
            subtitle: 'How you make money',
            bullets: [
              'Revenue Model: [Subscription/Transaction/Freemium/etc.]',
              'Pricing Strategy: $[X] per [unit/month/transaction]',
              'Customer Acquisition Cost (CAC): $[X]',
              'Customer Lifetime Value (LTV): $[X]',
              'LTV:CAC Ratio: [X]:1'
            ],
            notes: 'Show unit economics and path to profitability.'
          }
        },
        {
          id: '6',
          type: 'content',
          content: {
            title: 'Traction & Validation',
            subtitle: 'Proof that your solution works',
            bullets: [
              '[Key metrics: users, revenue, growth rate]',
              '[Customer testimonials or case studies]',
              '[Partnerships or pilot programs]',
              '[Product milestones achieved]',
              '[Awards or recognition received]'
            ],
            notes: 'Use charts and graphs to show growth trends.'
          }
        },
        {
          id: '7',
          type: 'content',
          content: {
            title: 'Competition & Competitive Advantage',
            subtitle: 'How you differentiate',
            bullets: [
              '[Direct and indirect competitors]',
              '[Competitive landscape analysis]',
              '[Your unique value proposition]',
              '[Barriers to entry you\'ve created]',
              '[Intellectual property or moats]'
            ],
            notes: 'Be honest about competition but highlight your advantages.'
          }
        },
        {
          id: '8',
          type: 'content',
          content: {
            title: 'Financial Projections',
            subtitle: '5-year revenue forecast',
            bullets: [
              'Year 1: $[X] revenue, [X] customers',
              'Year 3: $[X] revenue, [X] customers',
              'Year 5: $[X] revenue, [X] customers',
              'Key assumptions driving growth',
              'Path to profitability: [timeline]'
            ],
            notes: 'Be conservative but ambitious. Show key drivers.'
          }
        },
        {
          id: '9',
          type: 'content',
          content: {
            title: 'The Team',
            subtitle: 'Why you\'re the right team to execute',
            bullets: [
              '[Founder 1]: [Background and relevant experience]',
              '[Founder 2]: [Background and relevant experience]',
              '[Key team members and their roles]',
              '[Advisory board members]',
              '[Previous startup experience or domain expertise]'
            ],
            notes: 'Include photos and highlight relevant achievements.'
          }
        },
        {
          id: '10',
          type: 'content',
          content: {
            title: 'Funding Ask',
            subtitle: 'Investment needed to reach next milestone',
            bullets: [
              'Raising: $[X] million in [Series A/Seed/etc.]',
              'Use of funds: [X]% product development, [X]% marketing, [X]% hiring',
              'Milestones to achieve with this funding',
              'Timeline: [X] months runway',
              'Next funding round: $[X] million in [timeframe]'
            ],
            notes: 'Be specific about how you\'ll use the money.'
          }
        },
        {
          id: '11',
          type: 'content',
          content: {
            title: 'Thank You',
            subtitle: 'Questions & Discussion',
            bullets: [
              '[Your contact information]',
              '[Company website and social media]',
              '[Demo or trial signup information]',
              'Let\'s discuss how we can work together'
            ],
            notes: 'End with a clear call to action.'
          }
        }
      ]
    },
    is_public: true,
    is_default: true,
    created_at: '2024-01-04T00:00:00Z',
    updated_at: '2024-01-04T00:00:00Z',
    tags: ['startup', 'pitch-deck', 'investment', 'fundraising'],
    difficulty_level: 'advanced',
    usage_count: 3247,
    rating: 4.9,
    preview_image: '/api/templates/4/preview',
    color_scheme: 'purple',
    industry: 'startup'
  },
  {
    id: '5',
    user_id: 'system',
    title: 'Sales Presentation Template',
    description: 'Professional sales presentation template for client meetings, product demos, and proposal presentations',
    type: 'presentation',
    content: {
      title: 'Sales Presentation',
      theme: 'professional-sales',
      slides: [
        {
          id: '1',
          type: 'title',
          content: {
            title: '[Your Company Name]',
            subtitle: 'Delivering Solutions That Drive Results',
            presenter: '[Your Name, Title]',
            date: '[Meeting Date]',
            client: '[Client Company Name]'
          }
        },
        {
          id: '2',
          type: 'content',
          content: {
            title: 'Agenda',
            subtitle: 'What we\'ll cover today',
            bullets: [
              'Understanding your business challenges',
              'Our proposed solution',
              'Implementation roadmap',
              'Investment and ROI',
              'Next steps'
            ],
            notes: 'Set expectations and show professionalism'
          }
        },
        {
          id: '3',
          type: 'content',
          content: {
            title: 'Understanding Your Needs',
            subtitle: 'Based on our discovery conversations',
            bullets: [
              '[Specific challenge 1 the client mentioned]',
              '[Specific challenge 2 the client mentioned]',
              '[Business impact of these challenges]',
              '[Client\'s success criteria and goals]',
              '[Timeline and constraints discussed]'
            ],
            notes: 'Show you listened and understand their specific situation'
          }
        },
        {
          id: '4',
          type: 'content',
          content: {
            title: 'Our Solution',
            subtitle: 'Tailored to address your specific needs',
            bullets: [
              '[Solution component 1] addresses [specific challenge]',
              '[Solution component 2] delivers [specific benefit]',
              '[Unique differentiator] that sets us apart',
              '[Integration capabilities] with your existing systems',
              '[Scalability features] for future growth'
            ],
            notes: 'Connect each feature directly to their stated needs'
          }
        },
        {
          id: '5',
          type: 'content',
          content: {
            title: 'Proven Results',
            subtitle: 'Success stories from similar clients',
            bullets: [
              '[Client A]: Achieved [specific result] in [timeframe]',
              '[Client B]: Reduced [metric] by [percentage]',
              '[Client C]: Increased [metric] by [amount]',
              '[Industry benchmark] vs our client average',
              '[Testimonial quote] from satisfied client'
            ],
            notes: 'Use case studies relevant to their industry/size'
          }
        },
        {
          id: '6',
          type: 'content',
          content: {
            title: 'Implementation Roadmap',
            subtitle: 'Your path to success',
            bullets: [
              'Phase 1 (Weeks 1-2): [Initial setup and configuration]',
              'Phase 2 (Weeks 3-6): [Core implementation and testing]',
              'Phase 3 (Weeks 7-8): [Training and go-live]',
              'Ongoing: [Support and optimization]',
              'Key milestones and success metrics'
            ],
            notes: 'Show clear timeline and minimize perceived risk'
          }
        },
        {
          id: '7',
          type: 'content',
          content: {
            title: 'Investment & ROI',
            subtitle: 'Value delivered vs investment required',
            bullets: [
              'Total investment: $[amount] over [timeframe]',
              'Expected ROI: [percentage] within [timeframe]',
              'Break-even point: [timeframe]',
              'Cost savings: $[amount] annually',
              'Revenue impact: $[amount] potential increase'
            ],
            notes: 'Focus on value and return, not just cost'
          }
        },
        {
          id: '8',
          type: 'content',
          content: {
            title: 'Why Choose Us',
            subtitle: 'Your trusted partner for success',
            bullets: [
              '[Years] of experience in [industry/domain]',
              '[Number] successful implementations',
              '[Percentage] client retention rate',
              '24/7 support and dedicated account management',
              '[Specific credential/certification/award]'
            ],
            notes: 'Build confidence in your ability to deliver'
          }
        },
        {
          id: '9',
          type: 'content',
          content: {
            title: 'Next Steps',
            subtitle: 'Moving forward together',
            bullets: [
              'Decision timeline: [when they need to decide]',
              'Contract and legal review process',
              'Implementation start date: [proposed date]',
              'Key stakeholders and approval process',
              'Questions and final discussion'
            ],
            notes: 'Create urgency and clear path forward'
          }
        },
        {
          id: '10',
          type: 'content',
          content: {
            title: 'Thank You',
            subtitle: 'Questions & Discussion',
            bullets: [
              'Contact: [your email and phone]',
              'Proposal valid until: [date]',
              'References available upon request',
              'Ready to start when you are'
            ],
            notes: 'End with confidence and availability'
          }
        }
      ]
    },
    is_public: true,
    is_default: true,
    created_at: '2024-01-05T00:00:00Z',
    updated_at: '2024-01-05T00:00:00Z',
    tags: ['sales', 'client-presentation', 'proposal', 'business-development'],
    difficulty_level: 'intermediate',
    usage_count: 2156,
    rating: 4.8,
    preview_image: '/api/templates/5/preview',
    color_scheme: 'blue',
    industry: 'sales'
  },
  // LETTER TEMPLATES
  {
    id: '6',
    user_id: 'system',
    title: 'Professional Cover Letter',
    description: 'Comprehensive cover letter template that highlights your qualifications and enthusiasm for the position',
    type: 'letter',
    content: {
      sender: {
        name: '[Your Full Name]',
        address: '[Your Street Address]',
        city_state_zip: '[City, State ZIP Code]',
        phone: '[Your Phone Number]',
        email: '[<EMAIL>]',
        date: '[Date]'
      },
      recipient: {
        name: '[Hiring Manager Name]',
        title: '[Hiring Manager Title]',
        company: '[Company Name]',
        address: '[Company Street Address]',
        city_state_zip: '[City, State ZIP Code]'
      },
      content: {
        greeting: 'Dear [Hiring Manager Name / Hiring Manager],',
        opening_paragraph: 'I am writing to express my strong interest in the [Position Title] role at [Company Name]. With [number] years of experience in [relevant field/industry] and a proven track record of [key achievement or skill], I am excited about the opportunity to contribute to your team and help [Company Name] achieve its goals.',
        body_paragraph_1: 'In my current role as [Current Position] at [Current Company], I have successfully [specific achievement #1 with quantifiable results]. This experience has strengthened my expertise in [relevant skill/area] and demonstrated my ability to [relevant capability]. Additionally, I have [specific achievement #2], which directly aligns with the requirements outlined in your job posting.',
        body_paragraph_2: 'What particularly attracts me to [Company Name] is [specific reason related to company mission, values, or recent news]. Your commitment to [company value/initiative] resonates with my professional values and career aspirations. I am eager to bring my skills in [relevant skills] and my passion for [relevant area] to contribute to [specific company goal or project].',
        closing_paragraph: 'I would welcome the opportunity to discuss how my background and enthusiasm can contribute to your team\'s success. Thank you for considering my application. I look forward to hearing from you and am available for an interview at your convenience.',
        closing: 'Sincerely,',
        signature: '[Your Full Name]'
      },
      formatting: {
        font: 'Times New Roman or Arial',
        font_size: '11-12pt',
        margins: '1 inch all sides',
        spacing: 'Single-spaced with space between paragraphs'
      },
      tips: [
        'Customize the letter for each specific job application',
        'Research the company and mention specific details',
        'Quantify your achievements with numbers and percentages',
        'Keep the letter to one page',
        'Proofread carefully for grammar and spelling errors'
      ]
    },
    is_public: true,
    is_default: true,
    created_at: '2024-01-06T00:00:00Z',
    updated_at: '2024-01-06T00:00:00Z',
    tags: ['cover-letter', 'job-application', 'professional', 'career'],
    difficulty_level: 'beginner',
    usage_count: 4521,
    rating: 4.8,
    preview_image: '/api/templates/6/preview',
    color_scheme: 'navy',
    industry: 'general'
  },
  {
    id: '7',
    user_id: 'system',
    title: 'Business Proposal Letter',
    description: 'Professional business proposal letter template for presenting business opportunities and partnerships',
    type: 'letter',
    content: {
      sender: {
        name: 'Michael Chen',
        title: 'Business Development Director',
        company: 'TechSolutions Inc.',
        address: '1234 Innovation Drive, Suite 500',
        city_state_zip: 'San Francisco, CA 94105',
        phone: '(*************',
        email: '<EMAIL>',
        website: 'www.techsolutions.com',
        date: 'March 15, 2024'
      },
      recipient: {
        name: '[Recipient Full Name]',
        title: '[Recipient Title]',
        company: '[Recipient Company Name]',
        address: '[Recipient Company Address]',
        city_state_zip: '[City, State ZIP Code]'
      },
      content: {
        subject: 'Business Proposal: [Brief Description of Proposal]',
        greeting: 'Dear [Mr./Ms. Last Name],',
        opening_paragraph: 'I hope this letter finds you well. I am writing to present a business opportunity that I believe would be mutually beneficial for both [Your Company] and [Recipient Company]. After researching your company\'s recent [expansion/initiatives/achievements], I am confident that our proposed partnership could help you achieve [specific business goal].',
        body_paragraph_1: 'Our company, [Your Company Name], specializes in [your core business/service]. Over the past [number] years, we have successfully [key achievement or track record]. We have identified an opportunity where our expertise in [specific area] could complement your strengths in [recipient\'s strength area] to create significant value in the [market/industry] sector.',
        body_paragraph_2: 'Specifically, we propose [detailed description of the proposal]. This partnership would enable [Recipient Company] to [specific benefit #1] while allowing us to [specific benefit for your company]. Based on our preliminary analysis, this collaboration could result in [quantifiable benefit, e.g., cost savings, revenue increase, market expansion].',
        body_paragraph_3: 'We have prepared a detailed proposal that outlines the implementation timeline, resource requirements, and projected outcomes. The proposal includes [mention key components like financial projections, risk analysis, success metrics]. We believe this initiative could be launched within [timeframe] and begin generating results by [timeline].',
        closing_paragraph: 'I would welcome the opportunity to discuss this proposal with you in person and answer any questions you may have. I am available for a meeting at your convenience and can provide additional documentation as needed. Thank you for considering this opportunity, and I look forward to the possibility of working together.',
        closing: 'Best regards,',
        signature: '[Your Full Name]\n[Your Title]\n[Your Company Name]\n[Your Phone Number]\n[Your Email]'
      },
      attachments: [
        'Detailed Business Proposal Document',
        'Company Profile and Credentials',
        'Financial Projections',
        'Implementation Timeline'
      ],
      follow_up: {
        timeline: 'Follow up within 1-2 weeks if no response',
        method: 'Phone call or email',
        next_steps: 'Schedule meeting to discuss proposal in detail'
      }
    },
    is_public: true,
    is_default: true,
    created_at: '2024-01-07T00:00:00Z',
    updated_at: '2024-01-07T00:00:00Z',
    tags: ['business-proposal', 'partnership', 'professional', 'opportunity'],
    difficulty_level: 'intermediate',
    usage_count: 1876,
    rating: 4.7,
    preview_image: '/api/templates/7/preview',
    color_scheme: 'gray',
    industry: 'business'
  },
  // CV TEMPLATES
  {
    id: '8',
    user_id: 'system',
    title: 'Academic CV Template',
    description: 'Comprehensive CV template for academic professionals, researchers, and professors seeking academic positions',
    type: 'cv',
    content: {
      personalInfo: {
        name: '[Your Full Name]',
        title: '[Your Academic Title, e.g., Ph.D., Professor]',
        department: '[Department Name]',
        institution: '[Current Institution]',
        address: '[Institution Address]',
        phone: '[Your Phone Number]',
        email: '[<EMAIL>]',
        website: '[your-academic-website.com]',
        orcid: '[ORCID ID]',
        google_scholar: '[Google Scholar Profile URL]',
        summary: 'Distinguished academic researcher with [X] years of experience in [field of study]. Expertise in [research areas] with a strong publication record and proven track record of securing research funding. Committed to advancing knowledge in [field] through innovative research and excellence in teaching.'
      },
      sections: [
        {
          id: 'education',
          title: 'Education',
          items: [
            {
              degree: 'Ph.D. in [Field of Study]',
              institution: '[University Name]',
              location: '[City, State/Country]',
              year: '[Year]',
              dissertation: 'Dissertation Title: "[Full Dissertation Title]"',
              advisor: 'Advisor: [Advisor Name]',
              honors: '[Summa Cum Laude, etc.]'
            },
            {
              degree: 'M.A./M.S. in [Field of Study]',
              institution: '[University Name]',
              location: '[City, State/Country]',
              year: '[Year]',
              thesis: 'Thesis: "[Thesis Title]"'
            },
            {
              degree: 'B.A./B.S. in [Field of Study]',
              institution: '[University Name]',
              location: '[City, State/Country]',
              year: '[Year]',
              honors: '[Magna Cum Laude, Phi Beta Kappa, etc.]'
            }
          ]
        },
        {
          id: 'academic-positions',
          title: 'Academic Positions',
          items: [
            {
              position: '[Current Position, e.g., Associate Professor]',
              department: '[Department Name]',
              institution: '[University Name]',
              location: '[City, State/Country]',
              duration: '[Start Year] - Present',
              responsibilities: [
                'Conduct research in [specific research areas]',
                'Teach undergraduate and graduate courses in [subjects]',
                'Supervise [number] graduate students and postdocs',
                'Serve on departmental and university committees'
              ]
            }
          ]
        },
        {
          id: 'research-interests',
          title: 'Research Interests',
          items: [
            {
              area: '[Primary Research Area]',
              description: 'Detailed description of research focus and methodologies'
            },
            {
              area: '[Secondary Research Area]',
              description: 'Description of additional research interests'
            }
          ]
        },
        {
          id: 'publications',
          title: 'Publications',
          items: [
            {
              category: 'Peer-Reviewed Journal Articles',
              publications: [
                '[Author names]. ([Year]). "[Article Title]." Journal Name, Volume(Issue), pages. DOI: [DOI]',
                '[Continue with additional publications in reverse chronological order]'
              ]
            },
            {
              category: 'Book Chapters',
              publications: [
                '[Author names]. ([Year]). "[Chapter Title]." In [Editor names] (Eds.), Book Title (pp. pages). Publisher.'
              ]
            },
            {
              category: 'Conference Proceedings',
              publications: [
                '[Author names]. ([Year]). "[Paper Title]." Proceedings of [Conference Name], pages.'
              ]
            }
          ]
        },
        {
          id: 'grants-funding',
          title: 'Grants and Funding',
          items: [
            {
              title: '[Grant Title]',
              agency: '[Funding Agency]',
              amount: '$[Amount]',
              duration: '[Start Year] - [End Year]',
              role: '[Principal Investigator/Co-Investigator]',
              status: '[Awarded/Pending]'
            }
          ]
        },
        {
          id: 'teaching',
          title: 'Teaching Experience',
          items: [
            {
              course: '[Course Number and Title]',
              level: '[Undergraduate/Graduate]',
              institution: '[University Name]',
              semesters: '[Semesters/Years Taught]',
              enrollment: '[Typical Enrollment]',
              evaluations: '[Teaching Evaluation Score if strong]'
            }
          ]
        },
        {
          id: 'conferences',
          title: 'Conference Presentations',
          items: [
            {
              type: 'Invited Talks',
              presentations: [
                '"[Presentation Title]." [Conference Name], [Location], [Date].'
              ]
            },
            {
              type: 'Contributed Papers',
              presentations: [
                '"[Presentation Title]." [Conference Name], [Location], [Date].'
              ]
            }
          ]
        },
        {
          id: 'awards-honors',
          title: 'Awards and Honors',
          items: [
            {
              award: '[Award Name]',
              organization: '[Awarding Organization]',
              year: '[Year]',
              description: '[Brief description if needed]'
            }
          ]
        },
        {
          id: 'service',
          title: 'Professional Service',
          items: [
            {
              category: 'Editorial Service',
              roles: [
                'Editorial Board Member, [Journal Name] ([Years])',
                'Reviewer for [Journal Names]'
              ]
            },
            {
              category: 'Professional Organizations',
              roles: [
                'Member, [Organization Name]',
                'Committee Member, [Committee Name] ([Years])'
              ]
            }
          ]
        },
        {
          id: 'skills',
          title: 'Technical Skills',
          items: [
            {
              category: 'Research Methods',
              skills: '[Specific methodologies and techniques]'
            },
            {
              category: 'Software/Programming',
              skills: '[Statistical software, programming languages, etc.]'
            },
            {
              category: 'Languages',
              skills: '[Languages and proficiency levels]'
            }
          ]
        }
      ]
    },
    is_public: true,
    is_default: true,
    created_at: '2024-01-08T00:00:00Z',
    updated_at: '2024-01-08T00:00:00Z',
    tags: ['academic', 'research', 'professor', 'phd'],
    difficulty_level: 'advanced',
    usage_count: 987,
    rating: 4.9,
    preview_image: '/api/templates/8/preview',
    color_scheme: 'indigo',
    industry: 'academia'
  },
  {
    id: '9',
    user_id: 'system',
    title: 'Medical Professional CV',
    description: 'Specialized CV template for physicians, medical professionals, and healthcare practitioners',
    type: 'cv',
    content: {
      personalInfo: {
        name: '[Dr. Your Full Name]',
        credentials: '[MD, DO, etc.]',
        specialty: '[Medical Specialty]',
        address: '[Your Address]',
        phone: '[Your Phone Number]',
        email: '[<EMAIL>]',
        medical_license: '[License Number and State]',
        npi_number: '[NPI Number]',
        summary: 'Board-certified [Specialty] physician with [X] years of clinical experience providing exceptional patient care. Expertise in [specific areas] with a commitment to evidence-based medicine and continuous professional development. Proven track record in [specific achievements or areas of excellence].'
      },
      sections: [
        {
          id: 'medical-education',
          title: 'Medical Education',
          items: [
            {
              degree: 'Doctor of Medicine (MD) / Doctor of Osteopathic Medicine (DO)',
              institution: '[Medical School Name]',
              location: '[City, State]',
              graduation: '[Graduation Year]',
              honors: '[Alpha Omega Alpha, Dean\'s List, etc.]',
              thesis: '[Research thesis if applicable]'
            },
            {
              degree: '[Undergraduate Degree]',
              institution: '[University Name]',
              location: '[City, State]',
              graduation: '[Graduation Year]',
              major: '[Major/Pre-med track]',
              honors: '[Magna Cum Laude, etc.]'
            }
          ]
        },
        {
          id: 'postgraduate-training',
          title: 'Postgraduate Medical Training',
          items: [
            {
              program: '[Fellowship Program]',
              specialty: '[Fellowship Specialty]',
              institution: '[Hospital/Institution Name]',
              location: '[City, State]',
              duration: '[Start Year] - [End Year]',
              description: 'Advanced training in [specific areas]'
            },
            {
              program: 'Residency',
              specialty: '[Residency Specialty]',
              institution: '[Hospital/Institution Name]',
              location: '[City, State]',
              duration: '[Start Year] - [End Year]',
              chief_resident: '[Yes/No - if applicable]'
            },
            {
              program: 'Internship',
              type: '[Rotating/Transitional/Specialty]',
              institution: '[Hospital Name]',
              location: '[City, State]',
              duration: '[Year]'
            }
          ]
        },
        {
          id: 'board-certifications',
          title: 'Board Certifications & Licenses',
          items: [
            {
              certification: 'Board Certified in [Specialty]',
              board: '[Certifying Board Name]',
              year: '[Year Certified]',
              expiration: '[Expiration Date]'
            },
            {
              license: 'Medical License - [State]',
              number: '[License Number]',
              expiration: '[Expiration Date]',
              status: 'Active'
            },
            {
              certification: 'DEA Registration',
              number: '[DEA Number]',
              expiration: '[Expiration Date]'
            }
          ]
        },
        {
          id: 'clinical-experience',
          title: 'Clinical Experience',
          items: [
            {
              position: '[Current Position]',
              institution: '[Hospital/Clinic/Practice Name]',
              location: '[City, State]',
              duration: '[Start Date] - Present',
              responsibilities: [
                'Provide comprehensive medical care for [patient population]',
                'Perform [specific procedures or treatments]',
                'Collaborate with multidisciplinary healthcare teams',
                'Supervise residents and medical students',
                'Participate in quality improvement initiatives'
              ],
              achievements: [
                '[Specific achievement with measurable impact]',
                '[Quality metrics or patient satisfaction scores]'
              ]
            }
          ]
        },
        {
          id: 'publications',
          title: 'Publications & Research',
          items: [
            {
              category: 'Peer-Reviewed Publications',
              publications: [
                '[Author names]. ([Year]). "[Article Title]." Journal Name, Volume(Issue), pages. PMID: [PMID]'
              ]
            },
            {
              category: 'Research Experience',
              projects: [
                {
                  title: '[Research Project Title]',
                  institution: '[Institution]',
                  duration: '[Duration]',
                  role: '[Your Role]',
                  description: 'Brief description of research and outcomes'
                }
              ]
            }
          ]
        },
        {
          id: 'professional-activities',
          title: 'Professional Activities',
          items: [
            {
              category: 'Hospital Committees',
              activities: [
                '[Committee Name] - [Role] ([Years])',
                '[Committee Name] - Member ([Years])'
              ]
            },
            {
              category: 'Professional Memberships',
              memberships: [
                '[Medical Society Name] - Member',
                '[Specialty Organization] - Member'
              ]
            },
            {
              category: 'Editorial/Review Activities',
              activities: [
                'Peer reviewer for [Journal Name]',
                'Editorial board member, [Journal Name]'
              ]
            }
          ]
        },
        {
          id: 'continuing-education',
          title: 'Continuing Medical Education',
          items: [
            {
              activity: '[CME Course/Conference Name]',
              provider: '[Provider Organization]',
              date: '[Date]',
              credits: '[Number] CME credits'
            }
          ]
        },
        {
          id: 'awards-honors',
          title: 'Awards and Honors',
          items: [
            {
              award: '[Award Name]',
              organization: '[Awarding Organization]',
              year: '[Year]',
              description: '[Brief description if needed]'
            }
          ]
        }
      ]
    },
    is_public: true,
    is_default: true,
    created_at: '2024-01-09T00:00:00Z',
    updated_at: '2024-01-09T00:00:00Z',
    tags: ['medical', 'physician', 'healthcare', 'clinical'],
    difficulty_level: 'advanced',
    usage_count: 756,
    rating: 4.8,
    preview_image: '/api/templates/9/preview',
    color_scheme: 'red',
    industry: 'healthcare'
  }
];

// Professional template library for DocMagic
// These templates provide comprehensive, industry-standard formats
// for creating professional documents across various fields and industries

// Export for backward compatibility
export const mockTemplates = professionalTemplates;

// Additional template categories for future expansion
export const templateCategories = {
  resume: professionalTemplates.filter(t => t.type === 'resume'),
  presentation: professionalTemplates.filter(t => t.type === 'presentation'),
  letter: professionalTemplates.filter(t => t.type === 'letter'),
  cv: professionalTemplates.filter(t => t.type === 'cv')
};

// Template metadata for enhanced functionality
export const templateMetadata = {
  industries: ['technology', 'marketing', 'healthcare', 'academia', 'business', 'sales', 'startup', 'general'],
  difficulty_levels: ['beginner', 'intermediate', 'advanced'],
  color_schemes: ['blue', 'green', 'teal', 'purple', 'navy', 'gray', 'indigo', 'red'],
  total_templates: professionalTemplates.length,
  last_updated: '2024-01-09T00:00:00Z'
};
