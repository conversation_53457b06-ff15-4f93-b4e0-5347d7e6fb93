import { SiteHeader } from "@/components/site-header";
import { SponsorBanner } from "@/components/sponsor-banner";
import { HeroSection } from "@/components/hero-section";
import { FeaturesSection } from "@/components/features-section";
import { TestimonialsSection } from "@/components/testimonials-section";
import { DocumentCard } from "@/components/document-card";
import { TooltipWithShortcut } from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import {
  File as FileIcon,
  FileText,
  Presentation as LayoutPresentation,
  Mail,
  Github,
  Twitter,
  Linkedin,
  HelpCircle,
  BookOpen,
  Users,
  Sparkles,
  Heart,
  Zap,
  Star,
  ArrowDown,
  Wand2,
  Shield,
  Globe,
  Coffee,
  ArrowRight,
  Trophy,
} from "lucide-react";
import ScrollToTop from "@/components/scroll-to-top";
import { CursorSettings } from "@/components/cursor-settings";


export default function Home() {
  return (
    <div id="top" className="min-h-screen flex flex-col">
      <SponsorBanner />
      <SiteHeader />
      <main className="flex-1">
        <HeroSection />

        {/* Quick Navigation Section */}
        <section className="py-12 sm:py-16 bg-gradient-to-r from-blue-50/30 via-background to-purple-50/30 border-y border-border/20">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-8">
              <h2 className="text-2xl sm:text-3xl font-bold bolt-gradient-text mb-4">
                Explore DocMagic
              </h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                Discover all the powerful features and tools available in DocMagic
              </p>
            </div>

            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4 sm:gap-6">
              <TooltipWithShortcut content="View your profile and account settings">
                <Link
                  href="/profile"
                  className="group flex flex-col items-center p-4 rounded-xl glass-effect border border-blue-200/30 hover:scale-105 transition-all duration-300"
                >
                  <div className="w-12 h-12 bolt-gradient rounded-xl flex items-center justify-center mb-3 group-hover:scale-110 transition-transform">
                    <Users className="h-6 w-6 text-white" />
                  </div>
                  <span className="text-sm font-medium text-center">Profile</span>
                </Link>
              </TooltipWithShortcut>

              <TooltipWithShortcut content="Browse and manage your templates">
                <Link
                  href="/templates"
                  className="group flex flex-col items-center p-4 rounded-xl glass-effect border border-purple-200/30 hover:scale-105 transition-all duration-300"
                >
                  <div className="w-12 h-12 cosmic-gradient rounded-xl flex items-center justify-center mb-3 group-hover:scale-110 transition-transform">
                    <BookOpen className="h-6 w-6 text-white" />
                  </div>
                  <span className="text-sm font-medium text-center">Templates</span>
                </Link>
              </TooltipWithShortcut>

              <TooltipWithShortcut content="View pricing plans and options">
                <Link
                  href="/pricing"
                  className="group flex flex-col items-center p-4 rounded-xl glass-effect border border-green-200/30 hover:scale-105 transition-all duration-300"
                >
                  <div className="w-12 h-12 forest-gradient rounded-xl flex items-center justify-center mb-3 group-hover:scale-110 transition-transform">
                    <Star className="h-6 w-6 text-white" />
                  </div>
                  <span className="text-sm font-medium text-center">Pricing</span>
                </Link>
              </TooltipWithShortcut>

              <TooltipWithShortcut content="Learn more about DocMagic">
                <Link
                  href="/about"
                  className="group flex flex-col items-center p-4 rounded-xl glass-effect border border-amber-200/30 hover:scale-105 transition-all duration-300"
                >
                  <div className="w-12 h-12 sunset-gradient rounded-xl flex items-center justify-center mb-3 group-hover:scale-110 transition-transform">
                    <Heart className="h-6 w-6 text-white" />
                  </div>
                  <span className="text-sm font-medium text-center">About</span>
                </Link>
              </TooltipWithShortcut>

              <TooltipWithShortcut content="Get help and contact support">
                <Link
                  href="/contact"
                  className="group flex flex-col items-center p-4 rounded-xl glass-effect border border-blue-200/30 hover:scale-105 transition-all duration-300"
                >
                  <div className="w-12 h-12 ocean-gradient rounded-xl flex items-center justify-center mb-3 group-hover:scale-110 transition-transform">
                    <HelpCircle className="h-6 w-6 text-white" />
                  </div>
                  <span className="text-sm font-medium text-center">Contact</span>
                </Link>
              </TooltipWithShortcut>

              <TooltipWithShortcut content="Access documentation and guides">
                <Link
                  href="/documentation"
                  className="group flex flex-col items-center p-4 rounded-xl glass-effect border border-indigo-200/30 hover:scale-105 transition-all duration-300"
                >
                  <div className="w-12 h-12 bolt-gradient rounded-xl flex items-center justify-center mb-3 group-hover:scale-110 transition-transform">
                    <BookOpen className="h-6 w-6 text-white" />
                  </div>
                  <span className="text-sm font-medium text-center">Docs</span>
                </Link>
              </TooltipWithShortcut>
            </div>
          </div>
        </section>

        {/* Enhanced Professional Document Types Section */}
        <section
          id="document-types"
          className="py-20 sm:py-28 lg:py-36 relative overflow-hidden section-header"
        >
          {/* Enhanced animated background elements */}
          <div className="absolute inset-0 mesh-gradient opacity-25"></div>

          {/* Enhanced floating orbs with better positioning */}
          <div className="floating-orb w-56 h-56 sm:w-80 sm:h-80 sunset-gradient opacity-15 top-20 -left-28 sm:-left-40"></div>
          <div className="floating-orb w-40 h-40 sm:w-60 sm:h-60 ocean-gradient opacity-20 -top-10 right-10 sm:right-20"></div>
          <div className="floating-orb w-64 h-64 sm:w-96 sm:h-96 bolt-gradient opacity-10 bottom-10 left-1/4"></div>

          {/* Enhanced grid pattern overlay */}
          <div
            className="absolute inset-0 opacity-[0.04]"
            style={{
              backgroundImage: `url("data:image/svg+xml,%3csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3e%3cg fill='none' fill-rule='evenodd'%3e%3cg fill='%23000000' fill-opacity='1'%3e%3ccircle cx='30' cy='30' r='1'/%3e%3c/g%3e%3c/g%3e%3c/svg%3e")`,
            }}
          />

          <div className="mx-auto max-w-7xl px-2 xs:px-3 sm:px-6 lg:px-8 relative z-10">
            {/* Enhanced section transition indicator */}
            <div className="flex justify-center mb-8 sm:mb-12 lg:mb-16">
              <TooltipWithShortcut content="Explore our AI-powered document types">
                <div className="group relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                  <div className="relative glass-effect p-3 sm:p-4 rounded-full animate-bounce cursor-pointer hover:scale-110 transition-transform duration-300 border border-blue-200/30">
                    <ArrowDown className="h-5 w-5 sm:h-6 sm:w-6 bolt-gradient-text" />
                  </div>
                </div>
              </TooltipWithShortcut>
            </div>

            <div className="mx-auto max-w-5xl text-center mb-12 sm:mb-16 lg:mb-20">
              {/* Enhanced badge */}
              <div className="inline-flex items-center gap-3 px-6 sm:px-8 py-3 sm:py-4 rounded-full glass-effect mb-8 sm:mb-10 shimmer border border-purple-200/30">
                <Sparkles className="h-5 w-5 sm:h-6 sm:w-6 text-yellow-500 animate-pulse" />
                <span className="text-base sm:text-lg font-semibold bolt-gradient-text">
                  AI Document Studio
                </span>
                <Wand2 className="h-5 w-5 sm:h-6 sm:w-6 text-blue-500 animate-bounce" />
              </div>

              {/* Enhanced heading */}
              <h2 className="modern-display text-4xl sm:text-5xl md:text-6xl lg:text-7xl text-center mb-8 sm:mb-10 leading-tight">
                <span className="block mb-2">Choose Your</span>
                <span className="bolt-gradient-text relative inline-block">
                  Perfect Document
                  <div className="absolute -top-2 sm:-top-3 -right-2 sm:-right-3">
                    <Star
                      className="h-6 w-6 sm:h-8 sm:w-8 lg:h-10 lg:w-10 text-yellow-500 animate-spin"
                      style={{ animationDuration: "4s" }}
                    />
                  </div>
                </span>
              </h2>

              {/* Enhanced subtitle */}
              <p className="modern-body text-lg sm:text-xl lg:text-2xl text-muted-foreground max-w-3xl lg:max-w-4xl mx-auto px-4 sm:px-0 leading-relaxed">
                Transform your ideas into{" "}
                <span className="font-bold text-amber-600">
                  professional documents
                </span>{" "}
                with our{" "}
                <span className="font-bold text-blue-600">
                  AI-powered generators
                </span>
                . Each tool is designed for{" "}
                <span className="font-bold bolt-gradient-text">
                  maximum impact
                </span>
                .
              </p>

              {/* Enhanced stats bar with better visual design */}
              <div className="mt-8 sm:mt-10 lg:mt-12 flex flex-wrap justify-center gap-4 sm:gap-6 lg:gap-8">
                <TooltipWithShortcut content="We support 5 different types of professional documents">
                  <div className="group relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-amber-400/20 to-orange-400/20 rounded-full blur-lg group-hover:blur-xl transition-all duration-300"></div>
                    <div className="relative glass-effect px-6 py-3 rounded-full hover:scale-105 transition-transform duration-300 cursor-pointer border border-amber-200/30">
                      <span className="bolt-gradient-text font-bold text-base sm:text-lg">
                        5+
                      </span>
                      <span className="text-muted-foreground text-sm ml-2">
                        Document Types
                      </span>
                    </div>
                  </div>
                </TooltipWithShortcut>
                <TooltipWithShortcut content="All documents are generated using advanced AI technology">
                  <div className="group relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-cyan-400/20 rounded-full blur-lg group-hover:blur-xl transition-all duration-300"></div>
                    <div className="relative glass-effect px-6 py-3 rounded-full hover:scale-105 transition-transform duration-300 cursor-pointer border border-blue-200/30">
                      <span className="bolt-gradient-text font-bold text-base sm:text-lg">
                        AI
                      </span>
                      <span className="text-muted-foreground text-sm ml-2">
                        Powered
                      </span>
                    </div>
                  </div>
                </TooltipWithShortcut>
                <TooltipWithShortcut content="Unlimited customization and infinite creative possibilities">
                  <div className="group relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-full blur-lg group-hover:blur-xl transition-all duration-300"></div>
                    <div className="relative glass-effect px-6 py-3 rounded-full hover:scale-105 transition-transform duration-300 cursor-pointer border border-purple-200/30">
                      <span className="bolt-gradient-text font-bold text-base sm:text-lg">
                        ∞
                      </span>
                      <span className="text-muted-foreground text-sm ml-2">
                        Possibilities
                      </span>
                    </div>
                  </div>
                </TooltipWithShortcut>
              </div>
            </div>

            {/* Enhanced document cards grid with better layout */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6 sm:gap-8 lg:gap-10 max-w-7xl mx-auto">
              <div className="animate-bounce-in delay-100 will-change-transform lg:col-span-1">
                <TooltipWithShortcut content="Create ATS-optimized resumes that get you hired">
                  <DocumentCard
                    title="Resume"
                    description="Craft compelling resumes tailored to your target role with AI-powered optimization and ATS-friendly formatting"
                    icon={<FileIcon className="h-6 w-6 sm:h-7 sm:w-7" />}
                    href="/resume"
                  />
                </TooltipWithShortcut>
              </div>
              <div className="animate-bounce-in delay-200 will-change-transform lg:col-span-1">
                <TooltipWithShortcut content="Generate stunning presentations with smart layouts and visuals">
                  <DocumentCard
                    title="Presentation"
                    description="Create beautiful slide decks with intelligent layouts, charts, and visuals that captivate your audience"
                    icon={
                      <LayoutPresentation className="h-6 w-6 sm:h-7 sm:w-7" />
                    }
                    href="/presentation"
                  />
                </TooltipWithShortcut>
              </div>
              <div className="animate-bounce-in delay-300 will-change-transform lg:col-span-1">
                <TooltipWithShortcut content="Build comprehensive CVs for academic and research positions">
                  <DocumentCard
                    title="CV"
                    description="Build detailed curriculum vitae that showcase your academic achievements and research experience"
                    icon={<FileText className="h-6 w-6 sm:h-7 sm:w-7" />}
                    href="/cv"
                  />
                </TooltipWithShortcut>
              </div>
              <div className="animate-bounce-in delay-400 will-change-transform lg:col-span-1">
                <TooltipWithShortcut content="Draft persuasive cover letters and professional correspondence">
                  <DocumentCard
                    title="Letter"
                    description="Write compelling cover letters and professional correspondence with perfect tone and formatting"
                    icon={<Mail className="h-6 w-6 sm:h-7 sm:w-7" />}
                    href="/letter"
                  />
                </TooltipWithShortcut>
              </div>
              <div className="animate-bounce-in delay-500 will-change-transform lg:col-span-1">
                <TooltipWithShortcut content="Create professional diagrams and flowcharts with Mermaid syntax">
                  <DocumentCard
                    title="Diagram"
                    description="Design professional flowcharts, system architectures, and process diagrams with live preview"
                    icon={<BookOpen className="h-6 w-6 sm:h-7 sm:w-7" />}
                    href="/diagram"
                  />
                </TooltipWithShortcut>
              </div>
            </div>

            {/* Enhanced call to action section */}
            <div className="text-center mt-12 sm:mt-16 lg:mt-20">
              <div className="group relative max-w-4xl mx-auto">
                {/* Enhanced background effects */}
                <div className="absolute inset-0 bg-gradient-to-r from-blue-400/10 via-purple-400/10 to-pink-400/10 rounded-3xl blur-2xl group-hover:blur-3xl transition-all duration-500"></div>
                <div className="relative professional-card p-8 sm:p-12 rounded-3xl hover:scale-105 transition-transform duration-300 overflow-hidden border border-blue-200/30">
                  {/* Enhanced shimmer effect */}
                  <div className="absolute inset-0 shimmer opacity-40"></div>

                  <div className="relative z-10">
                    <div className="flex items-center justify-center gap-3 mb-6">
                      <Zap className="h-6 w-6 sm:h-8 sm:w-8 text-yellow-500 animate-pulse" />
                      <span className="font-bold bolt-gradient-text text-xl sm:text-2xl lg:text-3xl">
                        Ready to Transform Your Ideas?
                      </span>
                      <Sparkles className="h-6 w-6 sm:h-8 sm:w-8 text-blue-500 animate-pulse" />
                    </div>
                    <p className="text-muted-foreground text-base sm:text-lg lg:text-xl mb-8 max-w-2xl mx-auto leading-relaxed">
                      Join thousands of professionals who trust DocMagic to create
                      documents that make an impact. Start your journey today.
                    </p>

                    {/* Enhanced action button */}
                    <div className="mb-8">
                      <Button
                        asChild
                        size="lg"
                        className="bolt-gradient text-white font-bold px-8 sm:px-12 py-4 sm:py-6 rounded-full hover:scale-105 focus:ring-4 focus:ring-blue-400 focus:outline-none transition-all duration-300 bolt-glow shadow-2xl text-base sm:text-lg group"
                        style={{ animation: "gradient-shift 4s ease infinite" }}
                      >
                        <Link
                          href="/resume"
                          className="flex items-center justify-center gap-3"
                        >
                          <Sparkles className="h-5 w-5 sm:h-6 sm:w-6 group-hover:animate-spin" />
                          <span>Get Started Free</span>
                          <ArrowRight className="h-5 w-5 sm:h-6 sm:w-6 group-hover:translate-x-1 transition-transform" />
                        </Link>
                      </Button>
                    </div>

                    {/* Enhanced stats */}
                    <div className="flex flex-col sm:flex-row gap-4 sm:gap-8 justify-center items-center">
                      <TooltipWithShortcut content="Over 10,000 documents have been successfully created by our users">
                        <div className="group relative">
                          <div className="absolute inset-0 bg-gradient-to-r from-amber-400/20 to-orange-400/20 rounded-full blur-lg group-hover:blur-xl transition-all duration-300"></div>
                          <div className="relative professional-card px-6 py-3 rounded-full hover:scale-105 transition-transform duration-300 cursor-pointer border border-amber-200/30">
                            <Star className="inline h-4 w-4 sm:h-5 sm:w-5 text-amber-500 mr-2" />
                            <span className="bolt-gradient-text font-bold text-sm sm:text-base">
                              10K+ Documents Created
                            </span>
                          </div>
                        </div>
                      </TooltipWithShortcut>
                      <TooltipWithShortcut content="98% of our users achieve their goals with DocMagic-generated documents">
                        <div className="group relative">
                          <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-cyan-400/20 rounded-full blur-lg group-hover:blur-xl transition-all duration-300"></div>
                          <div className="relative professional-card px-6 py-3 rounded-full hover:scale-105 transition-transform duration-300 cursor-pointer border border-blue-200/30">
                            <Zap className="inline h-4 w-4 sm:h-5 sm:w-5 text-blue-500 mr-2" />
                            <span className="bolt-gradient-text font-bold text-sm sm:text-base">
                              98% Success Rate
                            </span>
                          </div>
                        </div>
                      </TooltipWithShortcut>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* New Value Proposition Section */}
        <section className="py-20 sm:py-28 lg:py-36 relative overflow-hidden bg-gradient-to-br from-blue-50/50 via-background to-purple-50/50">
          {/* Background elements */}
          <div className="absolute inset-0 mesh-gradient opacity-20"></div>
          <div className="floating-orb w-64 h-64 sm:w-96 sm:h-96 cosmic-gradient opacity-10 top-20 -right-32"></div>
          <div className="floating-orb w-48 h-48 sm:w-72 sm:h-72 forest-gradient opacity-15 bottom-20 -left-24"></div>

          <div className="mx-auto max-w-7xl px-2 xs:px-3 sm:px-6 lg:px-8 relative z-10">
            <div className="text-center mb-16 sm:mb-20">
              <div className="inline-flex items-center gap-3 px-6 py-3 rounded-full glass-effect mb-8 border border-green-200/30">
                <Shield className="h-5 w-5 text-green-600" />
                <span className="text-base font-semibold text-green-700">Why Choose DocMagic</span>
                <Trophy className="h-5 w-5 text-green-600" />
              </div>

              <h2 className="modern-display text-4xl sm:text-5xl md:text-6xl lg:text-7xl mb-8 leading-tight">
                <span className="block mb-2">The Smart Choice for</span>
                <span className="bolt-gradient-text">Professional Documents</span>
              </h2>

              <p className="modern-body text-lg sm:text-xl lg:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                Experience the future of document creation with AI that understands your needs and delivers exceptional results every time.
              </p>
            </div>

            {/* Value propositions grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-12">
              {/* Speed & Efficiency */}
              <div className="group relative">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-400/10 to-cyan-400/10 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                <div className="relative professional-card p-8 rounded-3xl hover:scale-105 transition-all duration-300 border border-blue-200/30 text-center">
                  <div className="w-16 h-16 mx-auto mb-6 bolt-gradient rounded-2xl flex items-center justify-center">
                    <Zap className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold mb-4 bolt-gradient-text">Lightning Fast</h3>
                  <p className="text-muted-foreground leading-relaxed">
                    Create professional documents in seconds, not hours. Our AI understands context and generates content that's ready to use immediately.
                  </p>
                </div>
              </div>

              {/* Quality & Precision */}
              <div className="group relative">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-400/10 to-pink-400/10 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                <div className="relative professional-card p-8 rounded-3xl hover:scale-105 transition-all duration-300 border border-purple-200/30 text-center">
                  <div className="w-16 h-16 mx-auto mb-6 cosmic-gradient rounded-2xl flex items-center justify-center">
                    <Star className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold mb-4 bolt-gradient-text">Premium Quality</h3>
                  <p className="text-muted-foreground leading-relaxed">
                    Every document is crafted with attention to detail, proper formatting, and professional standards that impress employers and clients.
                  </p>
                </div>
              </div>

              {/* Customization */}
              <div className="group relative md:col-span-2 lg:col-span-1">
                <div className="absolute inset-0 bg-gradient-to-r from-emerald-400/10 to-teal-400/10 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                <div className="relative professional-card p-8 rounded-3xl hover:scale-105 transition-all duration-300 border border-emerald-200/30 text-center">
                  <div className="w-16 h-16 mx-auto mb-6 forest-gradient rounded-2xl flex items-center justify-center">
                    <Wand2 className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold mb-4 bolt-gradient-text">Fully Customizable</h3>
                  <p className="text-muted-foreground leading-relaxed">
                    Tailor every aspect to your needs. From content to design, our AI adapts to your style and requirements for perfect results.
                  </p>
                </div>
              </div>
            </div>

            {/* Bottom CTA */}
            <div className="text-center mt-16 sm:mt-20">
              <div className="inline-flex items-center gap-4 px-8 py-4 rounded-full glass-effect border border-amber-200/30 hover:scale-105 transition-transform duration-300">
                <Users className="h-5 w-5 text-amber-600" />
                <span className="text-base font-semibold text-amber-700">Join 10,000+ satisfied users</span>
                <ArrowRight className="h-5 w-5 text-amber-600" />
              </div>
            </div>
          </div>
        </section>

        <FeaturesSection />
        <TestimonialsSection />
        <ScrollToTop /> 
        {/* added ScrollToTop component */}
      </main>
      
      {/* Cursor Settings Controls */}
      <CursorSettings />

      {/* Enhanced Professional Footer */}
      <footer className="relative overflow-hidden bg-gradient-to-br from-slate-50/50 via-background to-blue-50/50 border-t border-border/50">
        {/* Enhanced background elements */}
        <div className="absolute inset-0 mesh-gradient opacity-15"></div>
        <div className="floating-orb w-64 h-64 sm:w-96 sm:h-96 bolt-gradient opacity-10 top-20 -left-32"></div>
        <div className="floating-orb w-48 h-48 sm:w-72 sm:h-72 sunset-gradient opacity-15 bottom-20 -right-24"></div>
        <div className="floating-orb w-56 h-56 sm:w-80 sm:h-80 ocean-gradient opacity-10 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"></div>

        {/* Enhanced grid pattern overlay */}
        <div
          className="absolute inset-0 opacity-[0.02]"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3e%3cg fill='none' fill-rule='evenodd'%3e%3cg fill='%23000000' fill-opacity='1'%3e%3ccircle cx='30' cy='30' r='1'/%3e%3c/g%3e%3c/g%3e%3c/svg%3e")`,
          }}
        />

        <div className="container px-4 sm:px-6 lg:px-8 py-16 sm:py-20 mx-auto relative z-10">
          {/* Enhanced footer content */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 sm:gap-12 mb-12 sm:mb-16">
            {/* Enhanced Brand Column */}
            <div className="space-y-6 text-center sm:text-left group lg:col-span-2">
              <div className="flex items-center justify-center sm:justify-start space-x-3">
                <div className="relative">
                  <div className="w-10 h-10 bolt-gradient rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <FileText className="h-5 w-5 text-white" />
                  </div>
                  <Sparkles className="absolute -top-1 -right-1 h-4 w-4 text-yellow-500 animate-pulse" />
                </div>
                <span className="font-bold text-2xl bolt-gradient-text">
                  DocMagic
                </span>
              </div>

              <p className="text-base text-muted-foreground leading-relaxed max-w-md group-hover:text-foreground/80 transition-colors">
                The future of document creation is here. Transform your ideas into
                professional documents with our{" "}
                <span className="bolt-gradient-text font-bold">
                  AI-powered platform
                </span>
                . Join thousands of professionals who trust DocMagic.
              </p>

              {/* Enhanced social links */}
              <div className="flex justify-center sm:justify-start space-x-3">
                <TooltipWithShortcut content="Follow us on Twitter for updates and tips">
                  <a
                    href="https://x.com/docmagictech"
                    className="group/social relative"
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-cyan-400/20 rounded-xl blur-lg group-hover/social:blur-xl transition-all duration-300"></div>
                    <div className="relative text-muted-foreground hover:text-blue-500 transition-all duration-300 hover:scale-110 glass-effect p-3 rounded-xl border border-blue-200/30">
                      <Twitter className="h-5 w-5" />
                    </div>
                  </a>
                </TooltipWithShortcut>
                <TooltipWithShortcut content="Connect with us on LinkedIn">
                  <a
                    href="https://www.linkedin.com/company/docmagic/"
                    className="group/social relative"
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-blue-800/20 rounded-xl blur-lg group-hover/social:blur-xl transition-all duration-300"></div>
                    <div className="relative text-muted-foreground hover:text-blue-600 transition-all duration-300 hover:scale-110 glass-effect p-3 rounded-xl border border-blue-200/30">
                      <Linkedin className="h-5 w-5" />
                    </div>
                  </a>
                </TooltipWithShortcut>
                <TooltipWithShortcut content="View our open source code on GitHub">
                  <a
                    href="https://github.com/Muneerali199/DocMagic"
                    className="group/social relative"
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-gray-400/20 to-gray-600/20 rounded-xl blur-lg group-hover/social:blur-xl transition-all duration-300"></div>
                    <div className="relative text-muted-foreground hover:text-gray-700 transition-all duration-300 hover:scale-110 glass-effect p-3 rounded-xl border border-gray-200/30">
                      <Github className="h-5 w-5" />
                    </div>
                  </a>
                </TooltipWithShortcut>
              </div>

              {/* Newsletter signup */}
              <div className="mt-8">
                <h4 className="text-sm font-semibold bolt-gradient-text mb-3">Stay Updated</h4>
                <div className="flex gap-2">
                  <input
                    type="email"
                    placeholder="Enter your email"
                    className="flex-1 px-4 py-2 rounded-xl border border-border/50 bg-background/50 backdrop-blur-sm text-sm focus:outline-none focus:ring-2 focus:ring-blue-400/50"
                  />
                  <button className="px-4 py-2 bolt-gradient text-white rounded-xl text-sm font-medium hover:scale-105 transition-transform duration-300">
                    Subscribe
                  </button>
                </div>
              </div>
            </div>

            {/* Enhanced Product Column */}
            <div className="space-y-6 text-center sm:text-left group">
              <div className="flex items-center justify-center sm:justify-start gap-2">
                <div className="w-6 h-6 bolt-gradient rounded-lg flex items-center justify-center">
                  <Zap className="h-3 w-3 text-white" />
                </div>
                <h3 className="text-base font-bold bolt-gradient-text">
                  Product
                </h3>
              </div>
              <ul className="space-y-4">
                {productLinks.map((link, index) => (
                  <li key={index}>
                    <TooltipWithShortcut content={getProductTooltip(link.name)}>
                      <a
                        href={link.href}
                        className="text-sm text-muted-foreground hover:bolt-gradient-text transition-all duration-300 hover:translate-x-1 inline-block font-medium"
                      >
                        {link.name}
                      </a>
                    </TooltipWithShortcut>
                  </li>
                ))}
              </ul>
            </div>

            {/* Enhanced Resources Column */}
            <div className="space-y-6 text-center sm:text-left group">
              <div className="flex items-center justify-center sm:justify-start gap-2">
                <div className="w-6 h-6 forest-gradient rounded-lg flex items-center justify-center">
                  <BookOpen className="h-3 w-3 text-white" />
                </div>
                <h3 className="text-base font-bold bolt-gradient-text">
                  Resources
                </h3>
              </div>
              <ul className="space-y-4">
                <li>
                  <TooltipWithShortcut content="Complete API documentation and guides">
                    <a
                      href="/documentation"
                      className="text-sm text-muted-foreground hover:bolt-gradient-text transition-all duration-300 font-medium"
                    >
                      Documentation
                    </a>
                  </TooltipWithShortcut>
                </li>
                <li>
                  <TooltipWithShortcut content="Get help and support from our team">
                    <a
                      href="/contact"
                      className="text-sm text-muted-foreground hover:bolt-gradient-text transition-all duration-300 font-medium"
                    >
                      Help Center
                    </a>
                  </TooltipWithShortcut>
                </li>
                <li>
                  <TooltipWithShortcut content="Join our community on GitHub">
                    <a
                      href="https://github.com/Muneerali199/DocMagic"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm text-muted-foreground hover:bolt-gradient-text transition-all duration-300 font-medium"
                    >
                      Community
                    </a>
                  </TooltipWithShortcut>
                </li>
                <li>
                  <TooltipWithShortcut content="View user settings and preferences">
                    <a
                      href="/settings"
                      className="text-sm text-muted-foreground hover:bolt-gradient-text transition-all duration-300 font-medium"
                    >
                      Settings
                    </a>
                  </TooltipWithShortcut>
                </li>
              </ul>
            </div>

            {/* Company Column - Enhanced with tooltips */}
            <div className="space-y-6 text-center sm:text-left group">
              <div className="flex items-center justify-center sm:justify-start gap-2">
                <Globe className="h-4 w-4 text-purple-500" />
                <h3 className="text-sm sm:text-base font-semibold bolt-gradient-text">
                  COMPANY
                </h3>
              </div>
              <ul className="space-y-3">
                {companyLinks.map((link, index) => (
                  <li key={index}>
                    <TooltipWithShortcut content={getCompanyTooltip(link.name)}>
                      <a
                        href={link.href}
                        className="text-sm sm:text-base text-muted-foreground hover:bolt-gradient-text transition-all duration-300 hover:translate-x-1 inline-block"
                      >
                        {link.name}
                      </a>
                    </TooltipWithShortcut>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Professional bottom section with tooltips */}
          <div className="border-t border-border pt-8 sm:pt-12">
            {/* Professional Stats section with tooltips */}
            <div className="flex flex-wrap justify-center gap-4 sm:gap-8 mb-8">
              <TooltipWithShortcut content="Your data is encrypted and kept completely secure">
                <div className="professional-card px-4 sm:px-6 py-3 sm:py-4 rounded-full hover:scale-105 transition-transform duration-300 cursor-pointer">
                  <div className="flex items-center gap-2">
                    <Shield className="h-4 w-4 text-emerald-500" />
                    <span className="text-xs sm:text-sm font-medium professional-text">
                      Secure & Private
                    </span>
                  </div>
                </div>
              </TooltipWithShortcut>
              <TooltipWithShortcut content="Generate documents in seconds with AI-powered speed">
                <div className="professional-card px-4 sm:px-6 py-3 sm:py-4 rounded-full hover:scale-105 transition-transform duration-300 cursor-pointer">
                  <div className="flex items-center gap-2">
                    <Zap className="h-4 w-4 text-amber-500" />
                    <span className="text-xs sm:text-sm font-medium professional-text">
                      Lightning Fast
                    </span>
                  </div>
                </div>
              </TooltipWithShortcut>
              <TooltipWithShortcut content="Rated 5 stars by thousands of satisfied users">
                <div className="professional-card px-4 sm:px-6 py-3 sm:py-4 rounded-full hover:scale-105 transition-transform duration-300 cursor-pointer">
                  <div className="flex items-center gap-2">
                    <Star className="h-4 w-4 text-blue-500" />
                    <span className="text-xs sm:text-sm font-medium professional-text">
                      5-Star Rated
                    </span>
                  </div>
                </div>
              </TooltipWithShortcut>
            </div>

            {/* Copyright and links with tooltips */}
            <div className="flex flex-col sm:flex-row justify-between items-center">
              <div className="flex items-center gap-2 mb-4 sm:mb-0">
                <p className="text-xs sm:text-sm text-muted-foreground">
                  &copy; {new Date().getFullYear()} DocMagic. Made with
                </p>
                <TooltipWithShortcut content="Built with passion for great user experience">
                  <Heart className="h-3 w-3 sm:h-4 sm:w-4 text-red-500 animate-pulse cursor-pointer" />
                </TooltipWithShortcut>
                <p className="text-xs sm:text-sm text-muted-foreground">and</p>
                <TooltipWithShortcut content="Fueled by lots of coffee and late nights">
                  <Coffee className="h-3 w-3 sm:h-4 sm:w-4 text-yellow-600 animate-bounce cursor-pointer" />
                </TooltipWithShortcut>
                <p className="text-xs sm:text-sm text-muted-foreground">
                  for the community
                </p>
              </div>

              <div className="flex flex-wrap gap-2 sm:gap-4 justify-center sm:justify-end">
                <TooltipWithShortcut content="Return to the top of the page">
                  <a
                    href="#top"
                    className="text-xs sm:text-sm text-muted-foreground hover:bolt-gradient-text transition-all duration-300 hover:scale-105"
                  >
                    ↑ Top
                  </a>
                </TooltipWithShortcut>
                <TooltipWithShortcut content="Return to the home page">
                  <a
                    href="/"
                    className="text-xs sm:text-sm text-muted-foreground hover:bolt-gradient-text transition-all duration-300 hover:scale-105"
                  >
                    Home
                  </a>
                </TooltipWithShortcut>
                <TooltipWithShortcut content="Read our privacy policy and data handling practices">
                  <a
                    href="/about"
                    className="text-xs sm:text-sm text-muted-foreground hover:bolt-gradient-text transition-all duration-300 hover:scale-105"
                  >
                    Privacy
                  </a>
                </TooltipWithShortcut>
                <TooltipWithShortcut content="View our terms of service and usage guidelines">
                  <a
                    href="/about"
                    className="text-xs sm:text-sm text-muted-foreground hover:bolt-gradient-text transition-all duration-300 hover:scale-105"
                  >
                    Terms
                  </a>
                </TooltipWithShortcut>
                <TooltipWithShortcut content="Learn about our cookie policy and preferences">
                  <a
                    href="/about"
                    className="text-xs sm:text-sm text-muted-foreground hover:bolt-gradient-text transition-all duration-300 hover:scale-105"
                  >
                    Cookies
                  </a>
                </TooltipWithShortcut>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}

// Helper functions for tooltip content
function getProductTooltip(productName: string): string {
  const tooltips = {
    Features: "Explore all the powerful features DocMagic offers",
    Pricing: "View our flexible pricing plans and options",
    Templates: "Browse our collection of professional templates",
    Integrations: "Connect DocMagic with your favorite tools",
  };
  return (
    tooltips[productName as keyof typeof tooltips] ||
    `Learn more about ${productName}`
  );
}

function getCompanyTooltip(companyName: string): string {
  const tooltips = {
    "About Us": "Learn about our mission, vision, and team",
    Careers: "Join our team and help shape the future of document creation",
    Blog: "Read our latest insights, tips, and updates",
    Contact: "Get in touch with our team for support or partnerships",
  };
  return (
    tooltips[companyName as keyof typeof tooltips] ||
    `Learn more about ${companyName}`
  );
}

const productLinks = [
  { name: "Features", href: "#how-it-works" },
  { name: "Pricing", href: "/pricing" },
  { name: "Templates", href: "/templates" },
  { name: "Integrations", href: "#" }
];

const companyLinks = [
  { name: "About Us", href: "/about" },
  { name: "Careers", href: "#" },
  { name: "Blog", href: "#" },
  { name: "Contact", href: "/contact" },
];
