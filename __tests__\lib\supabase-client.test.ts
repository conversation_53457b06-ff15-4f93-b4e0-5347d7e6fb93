import { createClient } from '@/lib/supabase/client'

// Mock environment variables
const originalEnv = process.env

beforeEach(() => {
  jest.resetModules()
  process.env = { ...originalEnv }
})

afterAll(() => {
  process.env = originalEnv
})

describe('Supabase Client', () => {
  it('creates mock client when using placeholder credentials', () => {
    process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://placeholder-url.supabase.co'
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'placeholder-anon-key'
    
    const client = createClient()
    
    expect(client).toBeDefined()
    expect(client.auth).toBeDefined()
    expect(client.from).toBeDefined()
  })

  it('mock client returns expected data structure', async () => {
    process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://placeholder-url.supabase.co'
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'placeholder-anon-key'
    
    const client = createClient()
    
    // Test auth methods
    const userResult = await client.auth.getUser()
    expect(userResult.data.user).toBeDefined()
    expect(userResult.error).toBeNull()
    
    // Test database methods
    const templatesQuery = client.from('templates').select().order('created_at')
    const templatesResult = await templatesQuery
    expect(templatesResult.data).toBeDefined()
    expect(Array.isArray(templatesResult.data)).toBe(true)
    expect(templatesResult.error).toBeNull()
  })

  it('mock client handles template queries correctly', async () => {
    process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://placeholder-url.supabase.co'
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'placeholder-anon-key'
    
    const client = createClient()
    
    // Test select all templates
    const allTemplates = await client.from('templates').select().order('created_at')
    expect(allTemplates.data).toBeDefined()
    expect(allTemplates.data.length).toBeGreaterThan(0)
    
    // Test select single template
    const singleTemplate = await client.from('templates').select().eq('id', '1').single()
    expect(singleTemplate.data).toBeDefined()
    expect(singleTemplate.data.id).toBe('1')
    
    // Test select non-existent template
    const nonExistentTemplate = await client.from('templates').select().eq('id', 'non-existent').single()
    expect(nonExistentTemplate.data).toBeNull()
    expect(nonExistentTemplate.error).toBeDefined()
  })

  it('mock client handles template insertion', async () => {
    process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://placeholder-url.supabase.co'
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'placeholder-anon-key'
    
    const client = createClient()
    
    const newTemplate = {
      title: 'New Test Template',
      description: 'A new test template',
      type: 'resume',
      content: { test: 'content' },
      is_public: false
    }
    
    const insertResult = await client.from('templates').insert(newTemplate).select().single()
    
    expect(insertResult.data).toBeDefined()
    expect(insertResult.data.title).toBe(newTemplate.title)
    expect(insertResult.data.id).toBeDefined()
    expect(insertResult.data.created_at).toBeDefined()
    expect(insertResult.error).toBeNull()
  })

  it('mock client handles template updates', async () => {
    process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://placeholder-url.supabase.co'
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'placeholder-anon-key'
    
    const client = createClient()
    
    const updateData = {
      title: 'Updated Template Title',
      description: 'Updated description'
    }
    
    const updateResult = await client
      .from('templates')
      .update(updateData)
      .eq('id', '1')
      .select()
      .single()
    
    expect(updateResult.data).toBeDefined()
    expect(updateResult.data.title).toBe(updateData.title)
    expect(updateResult.data.description).toBe(updateData.description)
    expect(updateResult.error).toBeNull()
  })

  it('mock client handles template deletion', async () => {
    process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://placeholder-url.supabase.co'
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'placeholder-anon-key'
    
    const client = createClient()
    
    const deleteResult = await client.from('templates').delete().eq('id', '1')
    
    expect(deleteResult.error).toBeNull()
  })

  it('mock client filters templates correctly', async () => {
    process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://placeholder-url.supabase.co'
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'placeholder-anon-key'
    
    const client = createClient()
    
    // Test filtering by type
    const resumeTemplates = await client
      .from('templates')
      .select()
      .eq('type', 'resume')
      .order('created_at')
    
    expect(resumeTemplates.data).toBeDefined()
    expect(Array.isArray(resumeTemplates.data)).toBe(true)
    
    // All returned templates should be of type 'resume'
    resumeTemplates.data.forEach(template => {
      expect(template.type).toBe('resume')
    })
  })

  it('handles missing environment variables gracefully', () => {
    delete process.env.NEXT_PUBLIC_SUPABASE_URL
    delete process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
    
    // Should not throw an error and should return mock client
    expect(() => createClient()).not.toThrow()
    
    const client = createClient()
    expect(client).toBeDefined()
    expect(client.auth).toBeDefined()
    expect(client.from).toBeDefined()
  })
})
