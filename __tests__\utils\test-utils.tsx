import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

// Mock ThemeProvider since it might not be available in test environment
const MockThemeProvider = ({ children }: { children: React.ReactNode }) => <>{children}</>;

// Create a custom render function that includes providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        cacheTime: 0,
      },
    },
  })

  return (
    <QueryClientProvider client={queryClient}>
      <MockThemeProvider>
        {children}
      </MockThemeProvider>
    </QueryClientProvider>
  )
}

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>,
) => render(ui, { wrapper: AllTheProviders, ...options })

export * from '@testing-library/react'
export { customRender as render }

// Mock data helpers
export const mockTemplate = {
  id: '1',
  user_id: 'user-1',
  title: 'Test Template',
  description: 'A test template',
  type: 'resume' as const,
  content: {
    personalInfo: {
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '+**********',
      location: 'Test City',
      website: 'example.com',
      summary: 'Test summary'
    },
    sections: []
  },
  is_public: true,
  is_default: false,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
  tags: ['test', 'mock'],
  difficulty_level: 'beginner' as const,
  usage_count: 100,
  rating: 4.5,
  preview_image: '/api/templates/1/preview',
  color_scheme: 'blue',
  industry: 'technology'
}

export const mockTemplates = [
  mockTemplate,
  {
    ...mockTemplate,
    id: '2',
    title: 'Creative Template',
    type: 'presentation' as const,
    tags: ['creative', 'design'],
    difficulty_level: 'intermediate' as const,
  },
  {
    ...mockTemplate,
    id: '3',
    title: 'Business Letter',
    type: 'letter' as const,
    tags: ['business', 'formal'],
    difficulty_level: 'advanced' as const,
  }
]

// Mock fetch responses
export const mockFetchSuccess = (data: any) => {
  return Promise.resolve({
    ok: true,
    status: 200,
    json: () => Promise.resolve(data),
  } as Response)
}

export const mockFetchError = (status = 500, message = 'Server Error') => {
  return Promise.resolve({
    ok: false,
    status,
    statusText: message,
    json: () => Promise.resolve({ error: message }),
  } as Response)
}

// Test helpers
export const waitForLoadingToFinish = () => 
  new Promise(resolve => setTimeout(resolve, 0))

export const createMockRouter = (overrides = {}) => ({
  push: jest.fn(),
  replace: jest.fn(),
  prefetch: jest.fn(),
  back: jest.fn(),
  forward: jest.fn(),
  refresh: jest.fn(),
  ...overrides,
})
