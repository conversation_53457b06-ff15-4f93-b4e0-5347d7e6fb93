{"name": "docmagic", "version": "1.0.0", "description": "🪄 Open source AI-powered document creation platform - Create professional resumes, presentations, CVs, and letters in seconds", "keywords": ["ai", "document-generation", "resume-builder", "presentation-maker", "cv-creator", "open-source", "nextjs", "typescript", "gemini-ai", "supabase"], "homepage": "https://docmagic1.netlify.app", "repository": {"type": "git", "url": "https://github.com/docmagic-ai/docmagic.git"}, "bugs": {"url": "https://github.com/docmagic-ai/docmagic/issues"}, "license": "MIT", "author": "Xenonesis", "contributors": [{"name": "Xenonesis", "url": "https://github.com/xenonesis"}], "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "setup-db": "node scripts/setup-database.js", "security-audit": "node scripts/security-audit.js", "security-check": "npm audit && npm run security-audit"}, "dependencies": {"@google/generative-ai": "^0.3.1", "@hookform/resolvers": "^3.10.0", "@netlify/plugin-nextjs": "^4.41.3", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@stripe/react-stripe-js": "^3.8.0", "@stripe/stripe-js": "^3.5.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.52.0", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "autoprefixer": "^10.4.19", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "docx": "^8.5.0", "embla-carousel-react": "^8.3.0", "esbuild": "^0.25.8", "eslint": "^8.56.0", "eslint-config-next": "14.1.0", "framer-motion": "^10.18.0", "html-to-image": "^1.11.13", "html2canvas": "^1.4.1", "input-otp": "^1.2.4", "jspdf": "^3.0.1", "lucide-react": "^0.446.0", "mammoth": "^1.9.1", "mermaid": "^11.9.0", "next": "^14.1.0", "next-themes": "^0.4.6", "nodemailer": "^6.10.1", "officeparser": "^5.2.0", "pdf-parse": "^1.1.1", "pptxgenjs": "^3.12.0", "react": "^18.2.0", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.61.1", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-resizable-panels": "^2.1.9", "react-simple-typewriter": "^5.0.1", "recharts": "^2.15.4", "sonner": "^1.7.4", "stripe": "^14.25.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "typescript": "^5.4.5", "vaul": "^0.9.9", "zod": "^3.25.76", "zustand": "^4.5.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bcryptjs": "^2.4.6", "@types/jest": "^29.5.12", "@types/next": "^8.0.7", "@types/node": "^20.11.28", "@types/nodemailer": "^6.4.17", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.19", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "postcss": "^8.5.6", "tailwindcss": "^3.4.17"}}