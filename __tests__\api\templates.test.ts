import { GET } from '@/app/api/templates/route'
import { NextRequest } from 'next/server'

// Mock the Supabase server client
jest.mock('@/lib/supabase/server', () => ({
  createRoute: jest.fn(() => ({
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        order: jest.fn(() => ({
          data: [
            {
              id: '1',
              title: 'Test Template',
              description: 'A test template',
              type: 'resume',
              is_public: true,
              created_at: '2024-01-01T00:00:00Z',
              updated_at: '2024-01-01T00:00:00Z'
            }
          ],
          error: null
        }))
      }))
    }))
  }))
}))

describe('/api/templates', () => {
  it('returns templates successfully', async () => {
    const request = new NextRequest('http://localhost:3000/api/templates')
    const response = await GET(request)
    
    expect(response.status).toBe(200)
    
    const data = await response.json()
    expect(Array.isArray(data)).toBe(true)
    expect(data.length).toBeGreaterThan(0)
    expect(data[0]).toHaveProperty('id')
    expect(data[0]).toHaveProperty('title')
    expect(data[0]).toHaveProperty('type')
  })

  it('filters templates by type', async () => {
    const request = new NextRequest('http://localhost:3000/api/templates?type=resume')
    const response = await GET(request)
    
    expect(response.status).toBe(200)
    
    const data = await response.json()
    expect(Array.isArray(data)).toBe(true)
  })

  it('limits templates when limit parameter is provided', async () => {
    const request = new NextRequest('http://localhost:3000/api/templates?limit=5')
    const response = await GET(request)
    
    expect(response.status).toBe(200)
    
    const data = await response.json()
    expect(Array.isArray(data)).toBe(true)
  })

  it('handles invalid type parameter', async () => {
    const request = new NextRequest('http://localhost:3000/api/templates?type=invalid')
    const response = await GET(request)
    
    expect(response.status).toBe(400)
    
    const data = await response.json()
    expect(data).toHaveProperty('error')
  })

  it('handles invalid limit parameter', async () => {
    const request = new NextRequest('http://localhost:3000/api/templates?limit=invalid')
    const response = await GET(request)
    
    expect(response.status).toBe(400)
    
    const data = await response.json()
    expect(data).toHaveProperty('error')
  })
})

describe('/api/templates/[id]/preview', () => {
  it('generates SVG preview for template', async () => {
    // This would require importing the preview route handler
    // For now, we'll test the concept
    const templateId = '1'
    const expectedSvgStart = '<svg'
    
    // Mock the preview generation
    const mockSvg = `<svg width="300" height="400" xmlns="http://www.w3.org/2000/svg">
      <rect width="300" height="400" fill="#f0f9ff" rx="8"/>
    </svg>`
    
    expect(mockSvg).toContain(expectedSvgStart)
    expect(mockSvg).toContain('width="300"')
    expect(mockSvg).toContain('height="400"')
  })

  it('uses different colors for different template types', () => {
    const colorSchemes = {
      '1': { bg: '#f0f9ff', accent: '#3b82f6', text: '#1e40af' },
      '2': { bg: '#faf5ff', accent: '#a855f7', text: '#7c3aed' },
    }
    
    expect(colorSchemes['1'].bg).toBe('#f0f9ff')
    expect(colorSchemes['2'].bg).toBe('#faf5ff')
    expect(colorSchemes['1'].accent).not.toBe(colorSchemes['2'].accent)
  })
})
