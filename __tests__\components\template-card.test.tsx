import { render, screen, fireEvent, waitFor } from '../utils/test-utils'
import { TemplateCard } from '@/components/templates/template-card'
import { mockTemplate } from '../utils/test-utils'

// Mock the router
const mockPush = jest.fn()
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}))

// Mock the toast hook
const mockToast = jest.fn()
jest.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: mockToast,
  }),
}))

describe('TemplateCard', () => {
  const defaultProps = {
    id: mockTemplate.id,
    title: mockTemplate.title,
    description: mockTemplate.description,
    type: mockTemplate.type,
    isPublic: mockTemplate.is_public,
    isOwner: true,
    onDelete: jest.fn(),
    onTogglePublic: jest.fn(),
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders template card with basic information', () => {
    render(<TemplateCard {...defaultProps} />)
    
    expect(screen.getByText(mockTemplate.title)).toBeInTheDocument()
    expect(screen.getByText(mockTemplate.description!)).toBeInTheDocument()
    expect(screen.getByText('Resume')).toBeInTheDocument()
    expect(screen.getByText('Public')).toBeInTheDocument()
  })

  it('renders with enhanced metadata when provided', () => {
    render(
      <TemplateCard 
        {...defaultProps} 
        tags={mockTemplate.tags}
        difficulty_level={mockTemplate.difficulty_level}
        usage_count={mockTemplate.usage_count}
        rating={mockTemplate.rating}
        preview_image={mockTemplate.preview_image}
        industry={mockTemplate.industry}
      />
    )
    
    expect(screen.getByText('beginner')).toBeInTheDocument()
    expect(screen.getByText('technology')).toBeInTheDocument()
    expect(screen.getByText('test')).toBeInTheDocument()
    expect(screen.getByText('4.5')).toBeInTheDocument()
  })

  it('navigates to template usage page when "Use Template" is clicked', () => {
    render(<TemplateCard {...defaultProps} />)
    
    const useButton = screen.getByText('Use Template')
    fireEvent.click(useButton)
    
    expect(mockPush).toHaveBeenCalledWith('/templates/1/use')
  })

  it('opens dropdown menu and shows edit option', () => {
    render(<TemplateCard {...defaultProps} />)
    
    const moreButton = screen.getByRole('button', { name: /more options/i })
    fireEvent.click(moreButton)
    
    expect(screen.getByText('Edit')).toBeInTheDocument()
  })

  it('calls onDelete when delete is confirmed', async () => {
    const onDelete = jest.fn()
    render(<TemplateCard {...defaultProps} onDelete={onDelete} />)
    
    // Open dropdown
    const moreButton = screen.getByRole('button', { name: /more options/i })
    fireEvent.click(moreButton)
    
    // Click delete
    const deleteButton = screen.getByText('Delete')
    fireEvent.click(deleteButton)
    
    // Confirm deletion
    const confirmButton = screen.getByText('Delete')
    fireEvent.click(confirmButton)
    
    await waitFor(() => {
      expect(onDelete).toHaveBeenCalledWith('1')
    })
  })

  it('renders in list view mode', () => {
    render(<TemplateCard {...defaultProps} viewMode="list" />)
    
    expect(screen.getByText(mockTemplate.title)).toBeInTheDocument()
    expect(screen.getByText('Use Template')).toBeInTheDocument()
  })

  it('shows make private button when template is public and user is owner', () => {
    render(<TemplateCard {...defaultProps} isPublic={true} isOwner={true} />)
    
    expect(screen.getByText('Make Private')).toBeInTheDocument()
  })

  it('shows make public button when template is private and user is owner', () => {
    render(<TemplateCard {...defaultProps} isPublic={false} isOwner={true} />)
    
    expect(screen.getByText('Make Public')).toBeInTheDocument()
  })

  it('does not show toggle public button when user is not owner', () => {
    render(<TemplateCard {...defaultProps} isOwner={false} />)
    
    expect(screen.queryByText('Make Private')).not.toBeInTheDocument()
    expect(screen.queryByText('Make Public')).not.toBeInTheDocument()
  })

  it('formats usage count correctly', () => {
    render(
      <TemplateCard 
        {...defaultProps} 
        usage_count={1500}
      />
    )
    
    expect(screen.getByText('1.5k')).toBeInTheDocument()
  })

  it('displays preview image when provided', () => {
    render(
      <TemplateCard 
        {...defaultProps} 
        preview_image="/test-image.jpg"
      />
    )
    
    const image = screen.getByAltText(`${mockTemplate.title} preview`)
    expect(image).toBeInTheDocument()
    expect(image).toHaveAttribute('src', '/test-image.jpg')
  })
})
