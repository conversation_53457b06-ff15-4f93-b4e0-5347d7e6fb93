describe('Test Setup', () => {
  it('should run tests successfully', () => {
    expect(true).toBe(true)
  })

  it('should have access to environment variables', () => {
    expect(process.env.NEXT_PUBLIC_SUPABASE_URL).toBeDefined()
    expect(process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY).toBeDefined()
  })

  it('should have fetch mocked', () => {
    expect(global.fetch).toBeDefined()
    expect(jest.isMockFunction(global.fetch)).toBe(true)
  })
})
