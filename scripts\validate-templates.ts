#!/usr/bin/env ts-node

/**
 * Template Validation Script for DocMagic
 * 
 * This script validates all templates in the professional template library
 * to ensure they meet quality standards and contain real, professional content.
 */

import { professionalTemplates } from '../lib/mock-templates';
import { validateTemplate, generateQualityReport } from '../templates/validation';

interface ValidationSummary {
  totalTemplates: number;
  validTemplates: number;
  invalidTemplates: number;
  averageScore: number;
  templateResults: Array<{
    id: string;
    title: string;
    type: string;
    isValid: boolean;
    score: number;
    errorCount: number;
    warningCount: number;
  }>;
}

function validateAllTemplates(): ValidationSummary {
  console.log('🔍 Validating DocMagic Professional Templates...\n');
  
  const results: ValidationSummary = {
    totalTemplates: professionalTemplates.length,
    validTemplates: 0,
    invalidTemplates: 0,
    averageScore: 0,
    templateResults: []
  };

  let totalScore = 0;

  professionalTemplates.forEach((template, index) => {
    console.log(`Validating ${index + 1}/${professionalTemplates.length}: ${template.title}`);
    
    const validation = validateTemplate({
      id: template.id,
      title: template.title,
      description: template.description,
      type: template.type,
      content: template.content,
      metadata: {
        industry: (template as any).industry,
        difficulty: (template as any).difficulty_level,
        tags: (template as any).tags,
        lastUpdated: template.updated_at
      }
    });

    const result = {
      id: template.id,
      title: template.title,
      type: template.type,
      isValid: validation.isValid,
      score: validation.score,
      errorCount: validation.errors.length,
      warningCount: validation.warnings.length
    };

    results.templateResults.push(result);
    totalScore += validation.score;

    if (validation.isValid) {
      results.validTemplates++;
      console.log(`  ✅ Valid (Score: ${validation.score}/100)`);
    } else {
      results.invalidTemplates++;
      console.log(`  ❌ Invalid (Score: ${validation.score}/100)`);
      console.log(`     Errors: ${validation.errors.length}, Warnings: ${validation.warnings.length}`);
    }

    if (validation.errors.length > 0) {
      validation.errors.forEach(error => {
        console.log(`     🔴 ${error}`);
      });
    }

    if (validation.warnings.length > 0 && validation.warnings.length <= 3) {
      validation.warnings.forEach(warning => {
        console.log(`     🟡 ${warning}`);
      });
    }

    console.log('');
  });

  results.averageScore = totalScore / results.totalTemplates;
  return results;
}

function generateDetailedReport(summary: ValidationSummary): void {
  console.log('\n📊 VALIDATION SUMMARY');
  console.log('='.repeat(50));
  console.log(`Total Templates: ${summary.totalTemplates}`);
  console.log(`Valid Templates: ${summary.validTemplates} (${((summary.validTemplates / summary.totalTemplates) * 100).toFixed(1)}%)`);
  console.log(`Invalid Templates: ${summary.invalidTemplates} (${((summary.invalidTemplates / summary.totalTemplates) * 100).toFixed(1)}%)`);
  console.log(`Average Quality Score: ${summary.averageScore.toFixed(1)}/100`);

  console.log('\n📈 SCORE DISTRIBUTION');
  console.log('-'.repeat(30));
  const scoreRanges = {
    'Excellent (90-100)': summary.templateResults.filter(t => t.score >= 90).length,
    'Good (80-89)': summary.templateResults.filter(t => t.score >= 80 && t.score < 90).length,
    'Fair (70-79)': summary.templateResults.filter(t => t.score >= 70 && t.score < 80).length,
    'Poor (60-69)': summary.templateResults.filter(t => t.score >= 60 && t.score < 70).length,
    'Failing (<60)': summary.templateResults.filter(t => t.score < 60).length
  };

  Object.entries(scoreRanges).forEach(([range, count]) => {
    const percentage = ((count / summary.totalTemplates) * 100).toFixed(1);
    console.log(`${range}: ${count} templates (${percentage}%)`);
  });

  console.log('\n📋 TEMPLATE BREAKDOWN BY TYPE');
  console.log('-'.repeat(35));
  const typeBreakdown = summary.templateResults.reduce((acc, template) => {
    if (!acc[template.type]) {
      acc[template.type] = { total: 0, valid: 0, avgScore: 0 };
    }
    acc[template.type].total++;
    if (template.isValid) acc[template.type].valid++;
    acc[template.type].avgScore += template.score;
    return acc;
  }, {} as Record<string, { total: number; valid: number; avgScore: number }>);

  Object.entries(typeBreakdown).forEach(([type, stats]) => {
    const avgScore = (stats.avgScore / stats.total).toFixed(1);
    const validPercentage = ((stats.valid / stats.total) * 100).toFixed(1);
    console.log(`${type.toUpperCase()}: ${stats.total} templates, ${stats.valid} valid (${validPercentage}%), avg score: ${avgScore}`);
  });

  console.log('\n🏆 TOP PERFORMING TEMPLATES');
  console.log('-'.repeat(40));
  const topTemplates = summary.templateResults
    .sort((a, b) => b.score - a.score)
    .slice(0, 5);

  topTemplates.forEach((template, index) => {
    const status = template.isValid ? '✅' : '❌';
    console.log(`${index + 1}. ${status} ${template.title} (${template.type}) - ${template.score}/100`);
  });

  if (summary.invalidTemplates > 0) {
    console.log('\n⚠️  TEMPLATES NEEDING ATTENTION');
    console.log('-'.repeat(45));
    const problemTemplates = summary.templateResults
      .filter(t => !t.isValid || t.score < 80)
      .sort((a, b) => a.score - b.score);

    problemTemplates.forEach(template => {
      const status = template.isValid ? '🟡' : '🔴';
      console.log(`${status} ${template.title} (${template.type}) - Score: ${template.score}/100`);
      if (template.errorCount > 0) {
        console.log(`   Errors: ${template.errorCount}, Warnings: ${template.warningCount}`);
      }
    });
  }

  console.log('\n✨ QUALITY ASSESSMENT');
  console.log('-'.repeat(25));
  if (summary.averageScore >= 90) {
    console.log('🌟 EXCELLENT: Templates meet the highest professional standards!');
  } else if (summary.averageScore >= 80) {
    console.log('👍 GOOD: Templates are professional quality with minor improvements needed.');
  } else if (summary.averageScore >= 70) {
    console.log('⚠️  FAIR: Templates need significant improvements to meet professional standards.');
  } else {
    console.log('❌ POOR: Templates require major revisions before production use.');
  }

  console.log('\n📝 RECOMMENDATIONS');
  console.log('-'.repeat(20));
  if (summary.invalidTemplates > 0) {
    console.log('• Fix validation errors in invalid templates');
  }
  if (summary.averageScore < 85) {
    console.log('• Add more specific, quantifiable examples');
    console.log('• Include industry-specific terminology and best practices');
    console.log('• Ensure all placeholder content is replaced with realistic examples');
  }
  if (summary.templateResults.some(t => t.warningCount > 5)) {
    console.log('• Address templates with high warning counts');
  }
  console.log('• Regular review and updates to maintain currency with industry standards');
}

function checkContentQuality(): void {
  console.log('\n🔍 CONTENT QUALITY ANALYSIS');
  console.log('='.repeat(40));

  const qualityChecks = {
    hasPlaceholders: 0,
    hasQuantifiableMetrics: 0,
    hasIndustryTerms: 0,
    hasRealExamples: 0,
    hasComprehensiveContent: 0
  };

  professionalTemplates.forEach(template => {
    const contentStr = JSON.stringify(template.content).toLowerCase();
    
    // Check for placeholder content
    if (contentStr.includes('[') || contentStr.includes('placeholder') || contentStr.includes('example.com')) {
      qualityChecks.hasPlaceholders++;
    }

    // Check for quantifiable metrics
    if (/\d+%|\$\d+|\d+k|\d+\+/.test(contentStr)) {
      qualityChecks.hasQuantifiableMetrics++;
    }

    // Check for industry-specific terms
    const industryTerms = ['api', 'database', 'framework', 'optimization', 'scalable', 'agile', 'roi', 'kpi'];
    if (industryTerms.some(term => contentStr.includes(term))) {
      qualityChecks.hasIndustryTerms++;
    }

    // Check for comprehensive content
    if (contentStr.length > 2000) {
      qualityChecks.hasComprehensiveContent++;
    }
  });

  const total = professionalTemplates.length;
  console.log(`Templates with placeholder content: ${qualityChecks.hasPlaceholders}/${total} (${((qualityChecks.hasPlaceholders/total)*100).toFixed(1)}%)`);
  console.log(`Templates with quantifiable metrics: ${qualityChecks.hasQuantifiableMetrics}/${total} (${((qualityChecks.hasQuantifiableMetrics/total)*100).toFixed(1)}%)`);
  console.log(`Templates with industry terminology: ${qualityChecks.hasIndustryTerms}/${total} (${((qualityChecks.hasIndustryTerms/total)*100).toFixed(1)}%)`);
  console.log(`Templates with comprehensive content: ${qualityChecks.hasComprehensiveContent}/${total} (${((qualityChecks.hasComprehensiveContent/total)*100).toFixed(1)}%)`);
}

// Main execution
function main(): void {
  console.log('🚀 DocMagic Template Quality Validation');
  console.log('=====================================\n');

  const summary = validateAllTemplates();
  generateDetailedReport(summary);
  checkContentQuality();

  console.log('\n✅ Validation complete!');
  
  // Exit with appropriate code
  process.exit(summary.invalidTemplates > 0 ? 1 : 0);
}

// Run the validation if this script is executed directly
if (require.main === module) {
  main();
}

export { validateAllTemplates, generateDetailedReport, checkContentQuality };
