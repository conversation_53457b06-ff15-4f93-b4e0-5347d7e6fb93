import { render, screen, fireEvent, waitFor } from '../utils/test-utils'
import { TemplateList } from '@/components/templates/template-list'
import { mockTemplates, mockFetchSuccess, mockFetchError } from '../utils/test-utils'

// Mock the router
const mockPush = jest.fn()
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}))

// Mock the toast hook
const mockToast = jest.fn()
jest.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: mockToast,
  }),
}))

// Mock fetch
const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>

describe('TemplateList', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockFetch.mockResolvedValue(mockFetchSuccess(mockTemplates))
  })

  it('renders template list with default props', async () => {
    render(<TemplateList />)
    
    expect(screen.getByText('Templates')).toBeInTheDocument()
    expect(screen.getByText('Choose from our collection of professional templates')).toBeInTheDocument()
    expect(screen.getByText('Create New Template')).toBeInTheDocument()
  })

  it('renders search input when showSearch is true', () => {
    render(<TemplateList showSearch={true} />)
    
    expect(screen.getByPlaceholderText('Search templates...')).toBeInTheDocument()
  })

  it('renders category tabs when showCategoryTabs is true', async () => {
    render(<TemplateList showCategoryTabs={true} />)
    
    await waitFor(() => {
      expect(screen.getByText('All Templates')).toBeInTheDocument()
      expect(screen.getByText('Resumes')).toBeInTheDocument()
      expect(screen.getByText('Presentations')).toBeInTheDocument()
    })
  })

  it('filters templates by search query', async () => {
    render(<TemplateList initialTemplates={mockTemplates} />)
    
    const searchInput = screen.getByPlaceholderText('Search templates...')
    fireEvent.change(searchInput, { target: { value: 'Creative' } })
    
    await waitFor(() => {
      expect(screen.getByText('Creative Template')).toBeInTheDocument()
      expect(screen.queryByText('Test Template')).not.toBeInTheDocument()
    })
  })

  it('filters templates by category', async () => {
    render(<TemplateList initialTemplates={mockTemplates} />)
    
    // Click on Presentations tab
    const presentationTab = screen.getByText('Presentations')
    fireEvent.click(presentationTab)
    
    await waitFor(() => {
      expect(screen.getByText('Creative Template')).toBeInTheDocument()
      expect(screen.queryByText('Test Template')).not.toBeInTheDocument()
    })
  })

  it('sorts templates by name', async () => {
    render(<TemplateList initialTemplates={mockTemplates} />)
    
    const sortSelect = screen.getByDisplayValue('Latest')
    fireEvent.click(sortSelect)
    
    const nameOption = screen.getByText('Name')
    fireEvent.click(nameOption)
    
    await waitFor(() => {
      const templateCards = screen.getAllByRole('heading', { level: 3 })
      expect(templateCards[0]).toHaveTextContent('Business Letter')
      expect(templateCards[1]).toHaveTextContent('Creative Template')
    })
  })

  it('switches between grid and list view modes', () => {
    render(<TemplateList initialTemplates={mockTemplates} />)
    
    const listViewButton = screen.getByRole('button', { name: /list view/i })
    fireEvent.click(listViewButton)
    
    // Check if the view mode changed (this would require checking the grid classes)
    expect(listViewButton).toHaveClass('bg-primary')
  })

  it('shows loading skeleton when loading', () => {
    render(<TemplateList />)
    
    expect(screen.getAllByTestId('skeleton')).toHaveLength(8)
  })

  it('shows error state when fetch fails', async () => {
    mockFetch.mockResolvedValue(mockFetchError(500, 'Server Error'))
    
    render(<TemplateList />)
    
    await waitFor(() => {
      expect(screen.getByText('Something went wrong')).toBeInTheDocument()
      expect(screen.getByText('Try Again')).toBeInTheDocument()
    })
  })

  it('shows network error state when offline', async () => {
    // Mock navigator.onLine
    Object.defineProperty(navigator, 'onLine', {
      writable: true,
      value: false,
    })
    
    mockFetch.mockResolvedValue(mockFetchError(0, 'Network Error'))
    
    render(<TemplateList />)
    
    await waitFor(() => {
      expect(screen.getByText('Connection Problem')).toBeInTheDocument()
      expect(screen.getByText('Please check your internet connection and try again.')).toBeInTheDocument()
    })
  })

  it('retries fetch when retry button is clicked', async () => {
    mockFetch.mockResolvedValueOnce(mockFetchError(500, 'Server Error'))
    mockFetch.mockResolvedValueOnce(mockFetchSuccess(mockTemplates))
    
    render(<TemplateList />)
    
    await waitFor(() => {
      expect(screen.getByText('Try Again')).toBeInTheDocument()
    })
    
    const retryButton = screen.getByText('Try Again')
    fireEvent.click(retryButton)
    
    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        title: 'Refreshed',
        description: 'Templates have been refreshed successfully.',
      })
    })
  })

  it('shows empty state when no templates match search', async () => {
    render(<TemplateList initialTemplates={mockTemplates} />)
    
    const searchInput = screen.getByPlaceholderText('Search templates...')
    fireEvent.change(searchInput, { target: { value: 'nonexistent' } })
    
    await waitFor(() => {
      expect(screen.getByText('No templates found')).toBeInTheDocument()
      expect(screen.getByText('Clear Search')).toBeInTheDocument()
    })
  })

  it('clears search when clear search button is clicked', async () => {
    render(<TemplateList initialTemplates={mockTemplates} />)
    
    const searchInput = screen.getByPlaceholderText('Search templates...')
    fireEvent.change(searchInput, { target: { value: 'nonexistent' } })
    
    await waitFor(() => {
      expect(screen.getByText('Clear Search')).toBeInTheDocument()
    })
    
    const clearButton = screen.getByText('Clear Search')
    fireEvent.click(clearButton)
    
    expect(searchInput).toHaveValue('')
  })

  it('navigates to create template page when create button is clicked', () => {
    render(<TemplateList />)
    
    const createButton = screen.getByText('Create New Template')
    fireEvent.click(createButton)
    
    expect(mockPush).toHaveBeenCalledWith('/templates/new')
  })

  it('does not show create button when showCreateButton is false', () => {
    render(<TemplateList showCreateButton={false} />)
    
    expect(screen.queryByText('Create New Template')).not.toBeInTheDocument()
  })

  it('shows template count in category tabs', async () => {
    render(<TemplateList initialTemplates={mockTemplates} showCategoryTabs={true} />)
    
    await waitFor(() => {
      expect(screen.getByText('3')).toBeInTheDocument() // All templates count
      expect(screen.getByText('1')).toBeInTheDocument() // Resume count
    })
  })
})
