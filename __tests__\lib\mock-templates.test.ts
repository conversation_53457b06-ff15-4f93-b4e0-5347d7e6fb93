import { mockTemplates, mockUser } from '@/lib/mock-templates'

describe('Mock Templates', () => {
  it('exports mockTemplates array', () => {
    expect(mockTemplates).toBeDefined()
    expect(Array.isArray(mockTemplates)).toBe(true)
    expect(mockTemplates.length).toBeGreaterThan(0)
  })

  it('exports mockUser object', () => {
    expect(mockUser).toBeDefined()
    expect(mockUser).toHaveProperty('id')
    expect(mockUser).toHaveProperty('email')
    expect(mockUser).toHaveProperty('name')
  })

  it('contains templates with required properties', () => {
    mockTemplates.forEach(template => {
      expect(template).toHaveProperty('id')
      expect(template).toHaveProperty('user_id')
      expect(template).toHaveProperty('title')
      expect(template).toHaveProperty('description')
      expect(template).toHaveProperty('type')
      expect(template).toHaveProperty('content')
      expect(template).toHaveProperty('is_public')
      expect(template).toHaveProperty('is_default')
      expect(template).toHaveProperty('created_at')
      expect(template).toHaveProperty('updated_at')
    })
  })

  it('contains templates of different types', () => {
    const types = mockTemplates.map(template => template.type)
    const uniqueTypes = [...new Set(types)]
    
    expect(uniqueTypes).toContain('resume')
    expect(uniqueTypes).toContain('presentation')
    expect(uniqueTypes).toContain('letter')
    expect(uniqueTypes).toContain('cv')
  })

  it('contains templates with enhanced metadata', () => {
    const templatesWithMetadata = mockTemplates.filter(template => 
      'tags' in template && 
      'difficulty_level' in template && 
      'usage_count' in template && 
      'rating' in template
    )
    
    expect(templatesWithMetadata.length).toBeGreaterThan(0)
  })

  it('has valid difficulty levels', () => {
    const validDifficultyLevels = ['beginner', 'intermediate', 'advanced']
    
    mockTemplates.forEach(template => {
      if ('difficulty_level' in template) {
        expect(validDifficultyLevels).toContain((template as any).difficulty_level)
      }
    })
  })

  it('has valid template types', () => {
    const validTypes = ['resume', 'presentation', 'letter', 'cv']
    
    mockTemplates.forEach(template => {
      expect(validTypes).toContain(template.type)
    })
  })

  it('has valid ratings between 0 and 5', () => {
    mockTemplates.forEach(template => {
      if ('rating' in template) {
        const rating = (template as any).rating
        expect(rating).toBeGreaterThanOrEqual(0)
        expect(rating).toBeLessThanOrEqual(5)
      }
    })
  })

  it('has positive usage counts', () => {
    mockTemplates.forEach(template => {
      if ('usage_count' in template) {
        const usageCount = (template as any).usage_count
        expect(usageCount).toBeGreaterThanOrEqual(0)
      }
    })
  })

  it('has valid preview image URLs', () => {
    mockTemplates.forEach(template => {
      if ('preview_image' in template) {
        const previewImage = (template as any).preview_image
        expect(previewImage).toMatch(/^\/api\/templates\/\d+\/preview$/)
      }
    })
  })

  it('has valid date formats', () => {
    mockTemplates.forEach(template => {
      expect(new Date(template.created_at)).toBeInstanceOf(Date)
      expect(new Date(template.updated_at)).toBeInstanceOf(Date)
      expect(new Date(template.created_at).getTime()).not.toBeNaN()
      expect(new Date(template.updated_at).getTime()).not.toBeNaN()
    })
  })

  it('has unique template IDs', () => {
    const ids = mockTemplates.map(template => template.id)
    const uniqueIds = [...new Set(ids)]
    
    expect(uniqueIds.length).toBe(mockTemplates.length)
  })

  it('has consistent user_id for all templates', () => {
    mockTemplates.forEach(template => {
      expect(template.user_id).toBe(mockUser.id)
    })
  })

  it('contains templates with proper content structure', () => {
    mockTemplates.forEach(template => {
      expect(template.content).toBeDefined()
      
      if (template.type === 'resume' || template.type === 'cv') {
        expect(template.content).toHaveProperty('personalInfo')
        expect(template.content).toHaveProperty('sections')
      } else if (template.type === 'presentation') {
        expect(template.content).toHaveProperty('title')
        expect(template.content).toHaveProperty('slides')
      } else if (template.type === 'letter') {
        expect(template.content).toHaveProperty('recipient')
        expect(template.content).toHaveProperty('content')
      }
    })
  })
})
