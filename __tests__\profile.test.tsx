import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { jest } from '@jest/globals';
import ProfilePage from '@/app/profile/page';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
}));

// Mock Supabase client
jest.mock('@/lib/supabase/client', () => ({
  createClient: () => ({
    auth: {
      getUser: jest.fn().mockResolvedValue({
        data: {
          user: {
            id: 'test-user-id',
            email: '<EMAIL>',
            user_metadata: {
              name: 'Test User',
              avatar_url: '',
            },
            created_at: '2024-01-01T00:00:00Z',
          },
        },
        error: null,
      }),
      updateUser: jest.fn().mockResolvedValue({ error: null }),
    },
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          order: jest.fn(() => ({
            limit: jest.fn().mockResolvedValue({
              data: [],
              error: null,
            }),
          })),
        })),
      })),
    })),
    storage: {
      from: jest.fn(() => ({
        upload: jest.fn().mockResolvedValue({ error: null }),
        getPublicUrl: jest.fn().mockReturnValue({
          data: { publicUrl: 'https://example.com/avatar.jpg' },
        }),
      })),
    },
  }),
}));

// Mock toast hook
jest.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: jest.fn(),
  }),
}));

// Mock SiteHeader component
jest.mock('@/components/site-header', () => ({
  SiteHeader: () => <div data-testid="site-header">Site Header</div>,
}));

describe('Profile Page', () => {
  it('renders profile page with navigation', async () => {
    render(<ProfilePage />);
    
    // Check if SiteHeader is rendered
    expect(screen.getByTestId('site-header')).toBeInTheDocument();
    
    // Wait for profile data to load
    await waitFor(() => {
      expect(screen.getByText('Profile')).toBeInTheDocument();
    });
  });

  it('displays user information correctly', async () => {
    render(<ProfilePage />);
    
    await waitFor(() => {
      expect(screen.getByText('Test User')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    });
  });

  it('allows editing profile information', async () => {
    render(<ProfilePage />);
    
    await waitFor(() => {
      const editButton = screen.getByText('Edit Profile');
      fireEvent.click(editButton);
    });

    // Check if form fields are now editable
    expect(screen.getByDisplayValue('Test User')).toBeInTheDocument();
    expect(screen.getByText('Save')).toBeInTheDocument();
    expect(screen.getByText('Cancel')).toBeInTheDocument();
  });

  it('shows real statistics instead of fake data', async () => {
    render(<ProfilePage />);
    
    await waitFor(() => {
      // Should show 0 for templates and documents since mock returns empty arrays
      expect(screen.getByText('0')).toBeInTheDocument();
    });
  });

  it('displays profile picture upload option in edit mode', async () => {
    render(<ProfilePage />);
    
    await waitFor(() => {
      const editButton = screen.getByText('Edit Profile');
      fireEvent.click(editButton);
    });

    // Camera icon should be visible in edit mode
    const cameraButton = screen.getByRole('button', { name: /camera/i });
    expect(cameraButton).toBeInTheDocument();
  });
});
